import { AlertCircle } from "lucide-react";
import { getTranslations } from 'next-intl/server';
import Link from 'next/link';

export default async function ErrorPage({
  searchParams,
}: {
  searchParams: { message: string };
}) {
  const t = await getTranslations('ErrorPage');

  const errorMessage = searchParams.message || t('defaultMessage');

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-6 text-center">
      <div className="mb-8">
        <AlertCircle className="h-16 w-16 text-[#194852]" />
      </div>

      <h1 className="text-4xl font-bold mb-4">
        {t('title')}
      </h1>

      <p className="text-gray-600 text-lg mb-8">{errorMessage}</p>

      <Link
        href="/dashboard"
        className="bg-black text-white px-8 py-4 text-lg font-medium rounded-lg cursor-pointer hover:bg-black/90 inline-block"
      >
        {t('continueToLogin')}
      </Link>
    </div>
  );
}
