"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronRight, Plus } from "lucide-react";
import { Input } from "@/components/ui/input";
import { createBrowserClient } from "@supabase/ssr";
import { useToastMessages } from "@/hooks/use-toast-messages";
import { FlowCard } from "@/components/flows/FlowCard";
import { STEP_TYPES } from "@/types/flows";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

type StepType = (typeof STEP_TYPES)[number];

export interface Step {
  id: number;
  uid: string;
  type: StepType;
  title: string;
  description?: string;
  position: number;
  flowId: number;
}

export interface Flow {
  id: number;
  uid: string;
  name: string;
  description?: string;
  position: number;
  steps?: Step[];
}

export default function FlowsPage() {
  const [flows, setFlows] = useState<Flow[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const toastMessages = useToastMessages();

  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  // Load flows from database on mount
  useEffect(() => {
    const fetchFlows = async () => {
      try {
        const { data: flowsData, error: flowsError } = await supabase
          .from("flows")
          .select(
            `
            *,
            steps (*)
          `
          )
          .order("position");

        if (flowsError) throw flowsError;

        setFlows(flowsData || []);
      } catch (error) {
        console.error("Error loading flows:", error);
        toastMessages.form.fetchError("flows");
      }
    };

    fetchFlows();
  }, [supabase]);

  const handleAddFlow = async () => {
    try {
      const newFlow = {
        uid: crypto.randomUUID(),
        name: "",
        description: "",
        position: flows.length + 1,
      };

      const { data, error } = await supabase
        .from("flows")
        .insert(newFlow)
        .select()
        .single();

      if (error) throw error;

      setFlows((prev) => [...prev, data]);
      toastMessages.form.createSuccess();
    } catch (error) {
      console.error("Error creating flow:", error);
      toastMessages.form.createError();
    }
  };

  const handleDeleteFlow = async (id: number) => {
    try {
      const { error } = await supabase.from("flows").delete().eq("id", id);

      if (error) throw error;

      setFlows((prev) => prev.filter((flow) => flow.id !== id));
      toastMessages.form.deleteSuccess();
    } catch (error) {
      console.error("Error deleting flow:", error);
      toastMessages.form.deleteError();
    }
  };

  const handleSaveFlow = async (updatedFlow: Flow) => {
    try {
      // Update flow
      const { error: flowError } = await supabase
        .from("flows")
        .update({
          name: updatedFlow.name,
          description: updatedFlow.description,
        })
        .eq("uid", updatedFlow.uid);

      if (flowError) throw flowError;

      // Update or create steps
      if (updatedFlow.steps) {
        const stepsToUpsert = updatedFlow.steps.map((step) => ({
          uid: step.uid || crypto.randomUUID(),
          type: step.type,
          title: step.title,
          description: step.description,
          position: step.position,
          flow_id: updatedFlow.id,
        }));

        const { error: stepsError } = await supabase
          .from("steps")
          .upsert(stepsToUpsert, {
            onConflict: "uid",
          });

        if (stepsError) throw stepsError;
      }

      setFlows((prev) =>
        prev.map((flow) => (flow.id === updatedFlow.id ? updatedFlow : flow))
      );

      toastMessages.form.saveSuccess();
    } catch (error) {
      console.error("Error saving flow:", error);
      toastMessages.form.saveError();
    }
  };

  // Filter flows based on search query
  const filteredFlows = flows.filter((flow) =>
    flow.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="p-6 pt-0">
      <div className="mb-6 space-y-4">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator>
              <ChevronRight className="h-4 w-4" />
            </BreadcrumbSeparator>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard/admin">Admin</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator>
              <ChevronRight className="h-4 w-4" />
            </BreadcrumbSeparator>
            <BreadcrumbItem>
              <BreadcrumbPage>Flows</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-[#194852]">Flows</h2>
          <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
            <Button
              className="bg-[#194852] hover:bg-[#194852]/80 text-white hidden sm:flex"
              onClick={handleAddFlow}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Flow
            </Button>
            <Input
              placeholder="Search flows..."
              className="w-full sm:max-w-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>
      <div className="p-6 space-y-6 bg-white rounded-lg shadow">
        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div className="flex items-center justify-between">
            <Button
              className="bg-[#194852] hover:bg-[#194852]/80 text-white sm:hidden"
              onClick={handleAddFlow}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Flow
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6">
          {filteredFlows.map((flow) => (
            <FlowCard
              key={flow.uid}
              flow={flow}
              onDelete={handleDeleteFlow}
              onSave={handleSaveFlow}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
