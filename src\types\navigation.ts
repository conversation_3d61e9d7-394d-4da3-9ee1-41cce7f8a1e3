import type { RbacConditions as CoreRbacConditions } from "@/types/lib/rbac";

export interface NavigationItem {
  labelKey: string // Translation key instead of hardcoded label
  icon: string
  href: string
  RbacConditions?: CoreRbacConditions;
  subItems?: NavigationItem[]
}

export interface NavigationSection {
  titleKey: string // Translation key instead of hardcoded title
  items: NavigationItem[]
  RbacConditions?: CoreRbacConditions;
}