TITLE: Setting Request Locale in Root Layout for Static Rendering (TypeScript)
DESCRIPTION: This code snippet shows how to use `setRequestLocale` within a root layout (`app/[locale]/layout.tsx`) to enable static rendering. It validates the incoming `locale` parameter against a list of supported locales, redirecting to a 404 page if invalid. By calling `setRequestLocale(locale)`, the current locale is made available to all Server Components, allowing `next-intl` APIs to function without opting into dynamic rendering.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/with-i18n-routing.mdx#_snippet_14

LANGUAGE: tsx
CODE:
```
import {setRequestLocale} from 'next-intl/server';
import {hasLocale} from 'next-intl';
import {notFound} from 'next/navigation';
import {routing} from '@/i18n/routing';

export default async function LocaleLayout({children, params}) {
  const {locale} = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  // Enable static rendering
  setRequestLocale(locale);

  return (
    // ...
  );
}
```

----------------------------------------

TITLE: Structuring a Next.js Form with Server-Side Internationalization
DESCRIPTION: This Server Component (`RegisterPage`) demonstrates how to build a form while keeping internationalization on the server. It uses `useTranslations` for labels and renders various Client Components (`RegisterForm`, `FormField`, `FormSubmitButton`) for interactive parts, passing translated labels as props. It also defines a server action `registerAction` for form submission.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/server-client-components.mdx#_snippet_7

LANGUAGE: tsx
CODE:
```
import {useTranslations} from 'next-intl';

// A Client Component, so that `useActionState` can be used
// to potentially display errors received after submission.
import RegisterForm from './RegisterForm';

// A Client Component, so that `useFormStatus` can be used
// to disable the input field during submission.
import FormField from './FormField';

// A Client Component, so that `useFormStatus` can be used
// to disable the submit button during submission.
import FormSubmitButton from './FormSubmitButton';

export default function RegisterPage() {
  const t = useTranslations('RegisterPage');

  function registerAction() {
    'use server';
    // ...
  }

  return (
    <RegisterForm action={registerAction}>
      <FormField label={t('firstName')} name="firstName" />
      <FormField label={t('lastName')} name="lastName" />
      <FormField label={t('email')} name="email" />
      <FormField label={t('password')} name="password" />
      <FormSubmitButton label={t('submit')} />
    </RegisterForm>
  );
}
```

----------------------------------------

TITLE: Caching Current Date for Consistent Time Across Components (TypeScript)
DESCRIPTION: This snippet defines a utility function `getNow` that uses React's `cache()` to ensure a single `Date` instance is created and reused across all components within a single request. This prevents timing differences and inconsistencies when multiple components need the current time.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/date-formatting-nextjs.mdx#_snippet_3

LANGUAGE: TypeScript
CODE:
```
import {cache} from 'react';

// The first component that calls `getNow()` will
// trigger the creation of the `Date` instance.
const getNow = cache(() => new Date());

export default getNow;
```

----------------------------------------

TITLE: Defining ICU Messages in JSON for Localization
DESCRIPTION: This JSON snippet illustrates the structure for defining localized messages using ICU message syntax. It includes examples of simple string interpolation (`title`), date formatting (`membership` using `date, short`), and complex cardinal pluralization (`followers`) based on a `count` variable, demonstrating how different forms are selected for zero, one, and other values.
SOURCE: https://github.com/amannn/next-intl/blob/main/packages/use-intl/README.md#_snippet_1

LANGUAGE: js
CODE:
```
// en.json
{
  "UserProfile": {
    "title": "{firstName}'s profile",
    "membership": "Member since {memberSince, date, short}",
    "followers": "{count, plural,\n                    =0 {No followers yet}\n                    =1 {One follower}\n                    other {# followers}\n                  }"
  }
}
```

----------------------------------------

TITLE: Configuring Server-Side Internationalization with getRequestConfig (TypeScript)
DESCRIPTION: This snippet demonstrates how to configure server-side internationalization using `getRequestConfig` from `next-intl/server`. It defines a default export function that receives `requestLocale` and returns an object containing `locale` and `messages`, which are used for internationalization in Server Components, Server Actions, and related server-only code. This configuration is created once per request and can access request-specific data like cookies or headers.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import {getRequestConfig} from 'next-intl/server';
import {routing} from '@/i18n/routing';

export default getRequestConfig(async ({requestLocale}) => {
  // ...

  return {
    locale,
    messages
    // ...
  };
});
```

----------------------------------------

TITLE: Generating Static Params for Locales in Next.js Layouts/Pages (TypeScript)
DESCRIPTION: This snippet demonstrates how to implement `generateStaticParams` to inform Next.js about all possible locale values for dynamic route segments. This function is crucial for enabling static rendering at build time, ensuring that routes like `/[locale]` are pre-rendered. It returns an array of objects, each containing a `locale` parameter.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/with-i18n-routing.mdx#_snippet_13

LANGUAGE: tsx
CODE:
```
import {routing} from '@/i18n/routing';

export function generateStaticParams() {
  return routing.locales.map((locale) => ({locale}));
}
```

----------------------------------------

TITLE: Using `useTranslations` Hook in Next.js with next-intl
DESCRIPTION: This snippet demonstrates how to use the `useTranslations` hook from `next-intl` within a React component to fetch and display localized messages. It shows interpolation for dynamic values like `firstName`, `memberSince`, and pluralization for `followers` count. The `UserProfile` namespace is used to scope the translations.
SOURCE: https://github.com/amannn/next-intl/blob/main/README.md#_snippet_0

LANGUAGE: jsx
CODE:
```
// UserProfile.tsx
import {useTranslations} from 'next-intl';

export default function UserProfile({user}) {
  const t = useTranslations('UserProfile');

  return (
    <section>
      <h1>{t('title', {firstName: user.firstName})}</h1>
      <p>{t('membership', {memberSince: user.memberSince})}</p>
      <p>{t('followers', {count: user.numFollowers})}</p>
    </section>
  );
}
```

----------------------------------------

TITLE: Using `useTranslations` Hook in React Components (Next.intl)
DESCRIPTION: Demonstrates the standard way to consume translations within a React component using the `useTranslations` hook from `next-intl`. It initializes a translation function `t` scoped to 'About' messages, which is then used to display a translated title.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/translations-outside-of-react-components.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import {useTranslations} from 'next-intl';

function About() {
  const t = useTranslations('About');
  return <h1>{t('title')}</h1>;
}
```

----------------------------------------

TITLE: Inferring ICU Argument Types with next-intl `t` function (TSX)
DESCRIPTION: This snippet demonstrates how `next-intl` automatically infers the required types for ICU message arguments based on the message string. It shows examples for various ICU formats like `string`, `Date`, `number`, `plural`, `select`, and `rich` tags, enabling autocompletion and early error detection in the IDE.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-4-0.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
// "Hello {name}"
t('message', {});
//           ^? {name: string}

// "It's {today, date, long}"
t('message', {});
//           ^? {today: Date}

// "Page {page, number} out of {total, number}"
t('message', {});
//           ^? {page: number, total: number}

// "You have {count, plural, =0 {no followers yet} one {one follower} other {# followers}}."
t('message', {});
//           ^? {count: number}

// "Country: {country, select, US {United States} CA {Canada} other {Other}"
t('message', {});
//           ^? {country: 'US' | 'CA' | (string & {})}

// "Please refer to the <link>guidelines</link>."
t.rich('message', {});
//                ^? {link: (chunks: ReactNode) => ReactNode}
```

----------------------------------------

TITLE: Defining ICU Messages in JSON for next-intl
DESCRIPTION: This JSON snippet illustrates how to define messages using ICU message syntax for `next-intl`. It includes examples of simple interpolation (`title`), date formatting (`membership`), and pluralization rules (`followers`) for different counts. The `UserProfile` key corresponds to the namespace used in the `useTranslations` hook.
SOURCE: https://github.com/amannn/next-intl/blob/main/README.md#_snippet_1

LANGUAGE: json
CODE:
```
// en.json
{
  "UserProfile": {
    "title": "{firstName}'s profile",
    "membership": "Member since {memberSince, date, short}",
    "followers": "{count, plural, \n                    =0 {No followers yet} \n                    =1 {One follower} \n                    other {# followers} \n                  }"
  }
}
```

----------------------------------------

TITLE: Generating Static Metadata with Locale Parameter (TypeScript)
DESCRIPTION: This example demonstrates how to use the `locale` parameter within Next.js's `generateMetadata` function to enable static rendering for page metadata. It retrieves the `locale` from `params` and passes it to `getTranslations` from `next-intl/server`, ensuring that the metadata, such as the page title, is correctly localized and can be pre-rendered at build time.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/with-i18n-routing.mdx#_snippet_16

LANGUAGE: tsx
CODE:
```
import {getTranslations} from 'next-intl/server';

export async function generateMetadata({params}) {
  const {locale} = await params;
  const t = await getTranslations({locale, namespace: 'Metadata'});

  return {
    title: t('title')
  };
}
```

----------------------------------------

TITLE: Creating Navigation APIs with Defined Locales (TSX)
DESCRIPTION: This snippet demonstrates the standard way to create locale-aware navigation APIs using `createNavigation`. It imports the function from `next-intl/navigation` and a local `routing` configuration (expected to include defined `locales`), then calls `createNavigation` with this configuration to destructure and export the internationalized `Link`, `redirect`, `usePathname`, `useRouter`, and `getPathname` functions/components.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import {createNavigation} from 'next-intl/navigation';
import {routing} from './routing';

export const {Link, redirect, usePathname, useRouter, getPathname} =
  createNavigation(routing);
```

----------------------------------------

TITLE: Configuring Next.js Middleware with next-intl (Basic)
DESCRIPTION: This snippet demonstrates the basic setup of 'next-intl' middleware in `src/middleware.ts`. It imports `createMiddleware` and routing configuration to handle internationalized paths, matching all pathnames except for API routes, internal Next.js paths, and files with extensions.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/with-i18n-routing.mdx#_snippet_6

LANGUAGE: tsx
CODE:
```
import createMiddleware from 'next-intl/middleware';
import {routing} from './i18n/routing';

export default createMiddleware(routing);

export const config = {
  // Match all pathnames except for
  // - … if they start with `/api`, `/trpc`, `/_next` or `/_vercel`
  // - … the ones containing a dot (e.g. `favicon.ico`)
  matcher: '/((?!api|trpc|_next|_vercel|.*\..*).*)'
};
```

----------------------------------------

TITLE: Configuring Message Loading with getRequestConfig (TypeScript)
DESCRIPTION: This code demonstrates how to use `getRequestConfig` from `next-intl/server` to define an asynchronous function for fetching messages. This configuration is crucial for loading locale-specific messages during the application's rendering process.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_13

LANGUAGE: tsx
CODE:
```
import {getRequestConfig} from 'next-intl/server';

export default getRequestConfig(async () => {
  return {
    messages: (await import(`../../messages/${locale}.json`)).default
    // ...
  };
});
```

----------------------------------------

TITLE: Using useTranslations Hook in React Component
DESCRIPTION: This snippet demonstrates how to use the `useTranslations` hook from `use-intl` within a React functional component. It shows how to retrieve a translation function `t` scoped to 'UserProfile' and use it to display localized messages, including interpolated values for `firstName` and `memberSince`, and handling pluralization for `followers` count.
SOURCE: https://github.com/amannn/next-intl/blob/main/packages/use-intl/README.md#_snippet_0

LANGUAGE: jsx
CODE:
```
// UserProfile.tsx
import {useTranslations} from 'use-intl';

export default function UserProfile({user}) {
  const t = useTranslations('UserProfile');

  return (
    <section>
      <h1>{t('title', {firstName: user.firstName})}</h1>
      <p>{t('membership', {memberSince: user.memberSince})}</p>
      <p>{t('followers', {count: user.numFollowers})}</p>
    </section>
  );
}
```

----------------------------------------

TITLE: Configuring Request-Specific Environment with next-intl/server (TypeScript)
DESCRIPTION: This snippet demonstrates how to configure request-specific environment properties (`now`, `timeZone`, `locale`) using `getRequestConfig` from `next-intl/server`. This centralized configuration helps prevent hydration mismatches by ensuring consistent values across server and client, allowing dynamic settings per user request.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/date-formatting-nextjs.mdx#_snippet_9

LANGUAGE: TypeScript
CODE:
```
import {getRequestConfig} from 'next-intl/server';

export default getRequestConfig(async () => ({
  // (opt-in to use a shared value across the app)
  now: new Date(),

  // (defaults to the server's time zone)
  timeZone: 'Europe/Berlin',

  // (requires an explicit preference)
  locale: 'en'

  // ...
}));
```

----------------------------------------

TITLE: Configuring Root Layout for Locale Handling in Next.js App Router
DESCRIPTION: This `src/app/[locale]/layout.tsx` snippet shows how to integrate 'next-intl' into the root layout. It validates the incoming locale, sets the `lang` attribute on the `<html>` tag, and wraps children with `NextIntlClientProvider` to make i18n configuration available to Client Components.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/with-i18n-routing.mdx#_snippet_10

LANGUAGE: tsx
CODE:
```
import {NextIntlClientProvider, hasLocale} from 'next-intl';
import {notFound} from 'next/navigation';
import {routing} from '@/i18n/routing';

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode;
  params: Promise<{locale: string}>;
}) {
  // Ensure that the incoming `locale` is valid
  const {locale} = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider>{children}</NextIntlClientProvider>
      </body>
    </html>
  );
}
```

----------------------------------------

TITLE: Creating next-intl Middleware in TypeScript
DESCRIPTION: This snippet demonstrates how to set up the `next-intl` middleware in a `middleware.ts` file. It imports `createMiddleware` and a `routing` configuration, then exports the middleware. Additionally, it defines a `config` object with a `matcher` regex to apply the middleware to all pathnames except those starting with `/api`, `/trpc`, `/_next`, `/_vercel`, or containing a dot (like file extensions).
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/middleware.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import createMiddleware from 'next-intl/middleware';
import {routing} from './i18n/routing';

export default createMiddleware(routing);

export const config = {
  // Match all pathnames except for
  // - … if they start with `/api`, `/trpc`, `/_next` or `/_vercel`
  // - … the ones containing a dot (e.g. `favicon.ico`)
  matcher: '/((?!api|trpc|_next|_vercel|.*\\..*).*)'
};
```

----------------------------------------

TITLE: Changing Current Page Locale (next-intl, TSX)
DESCRIPTION: Demonstrates how to programmatically change the locale for the current page. It combines `usePathname` to get the current route and `useRouter` to navigate to the same route with a different locale. It shows variations for configurations *without* and *with* the `pathnames` setting, highlighting the need to pass `params` when `pathnames` is enabled. Requires `usePathname` and `useRouter` from `next-intl/navigation` and `useParams` from `next/navigation`.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_11

LANGUAGE: TSX
CODE:
```
'use client';

import {usePathname, useRouter} from '@/i18n/navigation';
import {useParams} from 'next/navigation';

const pathname = usePathname();
const router = useRouter();

// Without `pathnames`: Pass the current `pathname`
router.replace(pathname, {locale: 'de'});

// With `pathnames`: Pass `params` as well
const params = useParams();
router.replace(
  // @ts-expect-error -- TypeScript will validate that only known `params`
  // are used in combination with a given `pathname`. Since the two will
  // always match for the current route, we can skip runtime checks.
  {pathname, params},
  {locale: 'de'}
);
```

----------------------------------------

TITLE: Using NextIntlClientProvider in RootLayout (TSX)
DESCRIPTION: This snippet demonstrates how to integrate `NextIntlClientProvider` within a Next.js `RootLayout` Server Component. It provides internationalization context to client components, inheriting locale and messages from the server.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_2

LANGUAGE: tsx
CODE:
```
import {NextIntlClientProvider} from 'next-intl';
import {getMessages} from 'next-intl/server';

export default async function RootLayout(/* ... */) {
  // ...

  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider>...</NextIntlClientProvider>
      </body>
    </html>
  );
}
```

----------------------------------------

TITLE: Defining a Message with Cardinal Pluralization - JSON
DESCRIPTION: This JSON snippet defines a message key 'message' that utilizes ICU message syntax for cardinal pluralization. It provides different forms based on the `count` value, including specific cases for zero and one, and a general 'other' case using the '#' marker for number formatting.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_14

LANGUAGE: json
CODE:
```
"message": "You have {count, plural, =0 {no followers yet} =1 {one follower} other {# followers}}."
```

----------------------------------------

TITLE: Basic Link Usage and Query Params (TSX)
DESCRIPTION: Demonstrates the basic usage of the `Link` component for navigation, including how to pass search parameters using the `query` property and how to explicitly set the target locale using the `locale` prop.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_2

LANGUAGE: tsx
CODE:
```
import {Link} from '@/i18n/navigation';

// When the user is on `/en`, the link will point to `/en/about`
<Link href="/about">About</Link>

// Search params can be added via `query`
<Link href={{pathname: "/users", query: {sortBy: 'name'}}}>Users</Link>

// You can override the `locale` to switch to another language
// (this will set the `hreflang` attribute on the anchor tag)
<Link href="/" locale="de">Switch to German</Link>
```

----------------------------------------

TITLE: Augmenting Messages Type for Strict Key Validation (TypeScript)
DESCRIPTION: This snippet shows how to augment the `Messages` type in `AppConfig` by importing a default locale message file. This enables `next-intl` to infer the message structure, providing strict type checking for message keys used with `useTranslations`.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/typescript.mdx#_snippet_7

LANGUAGE: ts
CODE:
```
import messages from './messages/en.json';

declare module 'next-intl' {
  interface AppConfig {
    // ...
    Messages: typeof messages;
  }
}
```

----------------------------------------

TITLE: Using `createNavigation` for Routing in Next-intl (TypeScript)
DESCRIPTION: This snippet illustrates the basic usage of the new `createNavigation` function, which unifies and supersedes previous navigation APIs. It shows how to import `createNavigation` and `defineRouting`, then destructure `Link`, `redirect`, `usePathname`, and `useRouter` for use in Next.js applications.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-3-22.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
import {createNavigation} from 'next-intl/navigation';
import {defineRouting} from 'next-intl/routing';

export const routing = defineRouting(/* ... */);

export const {Link, redirect, usePathname, useRouter} =
  createNavigation(routing);
```

----------------------------------------

TITLE: Using next-intl Translations in Client Components (useTranslations)
DESCRIPTION: This `src/app/[locale]/page.tsx` example illustrates how to use the `useTranslations` hook within a Client Component. It fetches translations for a specific namespace ('HomePage') and renders localized text and links using `next-intl/navigation`'s `Link` component.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/with-i18n-routing.mdx#_snippet_11

LANGUAGE: tsx
CODE:
```
import {useTranslations} from 'next-intl';
import {Link} from '@/i18n/navigation';

export default function HomePage() {
  const t = useTranslations('HomePage');
  return (
    <div>
      <h1>{t('title')}</h1>
      <Link href="/about">{t('about')}</Link>
    </div>
  );
}
```

----------------------------------------

TITLE: Setting up i18n Navigation APIs
DESCRIPTION: This code sets up `next-intl`'s navigation APIs by wrapping Next.js's native navigation functions. It uses the previously defined routing configuration to provide locale-aware `Link`, `redirect`, `usePathname`, `useRouter`, and `getPathname` utilities, simplifying i18n navigation.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/with-i18n-routing.mdx#_snippet_5

LANGUAGE: ts
CODE:
```
import {createNavigation} from 'next-intl/navigation';
import {routing} from './routing';

// Lightweight wrappers around Next.js' navigation
// APIs that consider the routing configuration
export const {Link, redirect, usePathname, useRouter, getPathname} =
  createNavigation(routing);
```

----------------------------------------

TITLE: Illustrating Hydration Mismatch in Date Formatting
DESCRIPTION: This snippet illustrates a common hydration mismatch issue in Next.js when `new Date()` is used in shared components. It shows how the `formatDistance` function can produce different results on the server (e.g., '1 hour ago') compared to the client (e.g., '8 days ago') due to the time difference between environments, leading to React hydration errors.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/date-formatting-nextjs.mdx#_snippet_1

LANGUAGE: TSX
CODE:
```
// Server: "1 hour ago"
formatDistance(published, now, {addSuffix: true})}

// Client: "8 days ago"
formatDistance(published, now, {addSuffix: true})}
```

----------------------------------------

TITLE: Using getTranslations in an Async Server Component (next-intl, TSX)
DESCRIPTION: This example illustrates the use of the awaitable `getTranslations` function from `next-intl/server` within an async React Server Component. This approach is suitable for components that primarily fetch data and cannot use React hooks. It demonstrates fetching user data and then translations for the 'ProfilePage' namespace, passing a dynamic username to the translation.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/server-client-components.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
import {getTranslations} from 'next-intl/server';

export default async function ProfilePage() {
  const user = await fetchUser();
  const t = await getTranslations('ProfilePage');

  return (
    <PageLayout title={t('title', {username: user.name})}>
      <UserDetails user={user} />
    </PageLayout>
  );
}
```

----------------------------------------

TITLE: Localizing Zod Validation Errors in Next.js Server Actions (TypeScript)
DESCRIPTION: This snippet illustrates how to integrate `next-intl/server` with Zod for localizing validation error messages within a Next.js Server Action. It uses Zod's `errorMap` feature to provide custom, localized messages based on the validation issue path, falling back to Zod's default error if no specific translation is found.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/actions-metadata-route-handlers.mdx#_snippet_2

LANGUAGE: tsx
CODE:
```
import {getTranslations} from 'next-intl/server';
import {loginUser} from '@/services/session';
import {z} from 'zod';

const loginFormSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1)
});

// ...

async function loginAction(data: FormData) {
  'use server';

  const t = await getTranslations('LoginForm');
  const values = Object.fromEntries(data);

  const result = loginFormSchema.safeParse(values, {
    errorMap(issue, ctx) {
      const path = issue.path.join('.');

      const message = {
        email: t('invalidEmail'),
        password: t('invalidPassword')
      }[path];

      return {message: message || ctx.defaultError};
    }
  });

  // ...
}
```

----------------------------------------

TITLE: Integrating next-intl with Root Layout - TypeScript
DESCRIPTION: This `RootLayout` component integrates `next-intl` by retrieving the current locale using `getLocale` and setting it on the `<html>` tag. It also wraps the application children with `NextIntlClientProvider` to make the i18n configuration available to Client Components.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/without-i18n-routing.mdx#_snippet_6

LANGUAGE: tsx
CODE:
```
import {NextIntlClientProvider} from 'next-intl';
import {getLocale} from 'next-intl/server';

export default async function RootLayout({
  children
}: {
  children: React.ReactNode;
}) {
  const locale = await getLocale();

  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider>{children}</NextIntlClientProvider>
      </body>
    </html>
  );
}
```

----------------------------------------

TITLE: Configuring Localized Pathnames with next-intl
DESCRIPTION: Defines the routing configuration for `next-intl`, mapping internal application paths to localized external paths for different locales. It supports static paths, dynamic segments, and catch-all segments.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_6

LANGUAGE: tsx
CODE:
```
import {defineRouting} from 'next-intl/routing';

export const routing = defineRouting({
  locales: ['en-US', 'en-UK', 'de'],
  defaultLocale: 'en-US',

  // The `pathnames` object holds pairs of internal and
  // external paths. Based on the locale, the external
  // paths are rewritten to the shared, internal ones.
  pathnames: {
    // If all locales use the same pathname, a single
    // external path can be used for all locales
    '/': '/',
    '/blog': '/blog',

    // If some locales use different paths, you can
    // specify the relevant external pathnames
    '/about': {
      de: '/ueber-uns'
    },

    // Dynamic params are supported via square brackets
    '/news/[articleSlug]': {
      de: '/neuigkeiten/[articleSlug]'
    },

    // Static pathnames that overlap with dynamic segments
    // will be prioritized over the dynamic segment
    '/news/just-in': {
      de: '/neuigkeiten/aktuell'
    },

    // Also (optional) catch-all segments are supported
    '/categories/[...slug]': {
      de: '/kategorien/[...slug]'
    }
  }
});
```

----------------------------------------

TITLE: Basic Internationalization in React Apps with use-intl
DESCRIPTION: This snippet demonstrates the fundamental setup for internationalization in a React application using `use-intl`. It shows how to define messages, wrap the application with `IntlProvider` to provide locale and messages, and then use the `useTranslations` hook within a component to retrieve and display translated messages with dynamic placeholders.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/core-library.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import {IntlProvider, useTranslations} from 'use-intl';

// You can get the messages from anywhere you like. You can also
// fetch them from within a component and then render the provider
// along with your app once you have the messages.
const messages = {
  App: {
    hello: 'Hello {username}!'
  }
};

function Root() {
  return (
    <IntlProvider messages={messages} locale="en">
      <App user={{name: 'Jane'}} />
    </IntlProvider>
  );
}

function App({user}) {
  const t = useTranslations('App');
  return <h1>{t('hello', {username: user.name})}</h1>;
}
```

----------------------------------------

TITLE: Using next-intl Translations in Server Components (getTranslations)
DESCRIPTION: This snippet from `src/app/[locale]/page.tsx` demonstrates how to fetch translations directly within an async Server Component. It uses the awaitable `getTranslations` function from `next-intl/server` to retrieve messages for a namespace ('HomePage') without requiring a client-side hook.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/with-i18n-routing.mdx#_snippet_12

LANGUAGE: tsx
CODE:
```
import {getTranslations} from 'next-intl/server';

export default async function HomePage() {
  const t = await getTranslations('HomePage');
  return <h1>{t('title')}</h1>;
}
```

----------------------------------------

TITLE: Using useTranslations in a Non-Async Server Component (next-intl, TSX)
DESCRIPTION: This snippet demonstrates how to use the `useTranslations` hook from `next-intl` within a regular (non-async) React Server Component. Since this component avoids interactive React features like `useState` or `useEffect`, it can be rendered entirely on the server, ensuring messages and library code remain server-side. It fetches translations for the 'HomePage' namespace.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/server-client-components.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import {useTranslations} from 'next-intl';

// Since this component doesn't use any interactive features
// from React, it can be run as a Server Component.

export default function HomePage() {
  const t = useTranslations('HomePage');
  return <h1>{t('title')}</h1>;
}
```

----------------------------------------

TITLE: Localizing Server Action Messages in Next.js (TypeScript)
DESCRIPTION: This example shows how to use `next-intl/server` within a Next.js Server Action to localize user-facing error messages. It retrieves translations for a specific namespace and returns a localized error message if credentials are invalid.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/actions-metadata-route-handlers.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
import {getTranslations} from 'next-intl/server';

async function loginAction(data: FormData) {
  'use server';

  const t = await getTranslations('LoginForm');
  const areCredentialsValid = /* ... */;
  if (!areCredentialsValid) {
    return {error: t('invalidCredentials')};
  }
}
```

----------------------------------------

TITLE: Generating Localized Sitemap with Alternate Entries in Next.js
DESCRIPTION: This snippet illustrates how to generate a sitemap in Next.js that includes locale-specific alternate entries using `next-intl`. It iterates through defined locales to create unique URLs for each page and includes `alternates` metadata, informing search engines about available localized versions of the content.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/actions-metadata-route-handlers.mdx#_snippet_6

LANGUAGE: TSX
CODE:
```
import {MetadataRoute} from 'next';
import {Locale} from 'next-intl';
import {getPathname} from '@/i18n/navigation';
import {routing} from '@/i18n/routing';

// Adapt this as necessary
const host = 'https://acme.com';

export default function sitemap(): MetadataRoute.Sitemap {
  // Adapt this as necessary
  return [...getEntries('/'), ...getEntries('/users')];
}

type Href = Parameters<typeof getPathname>[0]['href'];

function getEntries(href: Href) {
  return routing.locales.map((locale) => ({
    url: getUrl(href, locale),
    alternates: {
      languages: Object.fromEntries(
        routing.locales.map((cur) => [cur, getUrl(href, cur)])
      )
    }
  }));
}

function getUrl(href: Href, locale: Locale) {
  const pathname = getPathname({locale, href});
  return host + pathname;
}
```

----------------------------------------

TITLE: Implementing a Server-Side Locale Switcher in Next.js
DESCRIPTION: This Server Component fetches the current locale and translations for a locale switcher. It renders a `LocaleSwitcherSelect` Client Component, passing the default locale and translated labels, and populates it with options for available locales, ensuring internationalization remains on the server side.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/server-client-components.mdx#_snippet_6

LANGUAGE: tsx
CODE:
```
import {useLocale, useTranslations} from 'next-intl';
import {locales} from '@/config';

// A Client Component that registers an event listener for
// the `change` event of the select, uses `useRouter`
// to change the locale and uses `useTransition` to display
// a loading state during the transition.
import LocaleSwitcherSelect from './LocaleSwitcherSelect';

export default function LocaleSwitcher() {
  const t = useTranslations('LocaleSwitcher');
  const locale = useLocale();

  return (
    <LocaleSwitcherSelect defaultValue={locale} label={t('label')}>
      {locales.map((cur) => (
        <option key={cur} value={cur}>
          {t('locale', {locale: cur})}
        </option>
      ))}
    </LocaleSwitcherSelect>
  );
}
```

----------------------------------------

TITLE: Validating Locale and Triggering 404 in Next.js Localized Layout
DESCRIPTION: This snippet demonstrates how to validate the incoming `locale` parameter within a localized layout (`app/[locale]/layout.tsx`). If the `locale` is not valid according to `next-intl`'s `hasLocale` function, it calls `notFound()` to render the appropriate 404 page, ensuring only valid localized requests proceed.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/error-files.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
import {hasLocale} from 'next-intl';
import {notFound} from 'next/navigation';
import {routing} from '@/i18n/routing';

export default function LocaleLayout({children, params}) {
  const {locale} = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  // ...
}
```

----------------------------------------

TITLE: Localized Date Formatting with Time Zone using date-fns-tz (TSX)
DESCRIPTION: This enhanced component formats a `Date` object considering both a specified `timeZone` and `locale` using `date-fns-tz`. It accepts `published`, `timeZone`, and `locale` as props, ensuring that dates are displayed not only in the correct time zone but also according to the user's preferred language and regional format.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/date-formatting-nextjs.mdx#_snippet_8

LANGUAGE: tsx
CODE:
```
import {format} from 'date-fns-tz';

type Props = {
  published: Date;
  timeZone: string;
  locale: string;
};

export default function BlogPostPublishedDate({
  published,
  timeZone,
  locale
}: Props) {
  return <p>{format(published, timeZone, 'MMM d, yyyy', {locale})}</p>;
}
```

----------------------------------------

TITLE: Using `getTranslations` Function in Next.js App Router (TSX)
DESCRIPTION: This snippet illustrates the use of the awaitable `getTranslations` function from `next-intl/server` for asynchronous components, typically server components, in the Next.js App Router. It asynchronously fetches the translation function `t` for the 'HomePage' namespace, enabling server-side translation rendering. This method is essential for components that need to fetch translations before rendering on the server.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/without-i18n-routing.mdx#_snippet_8

LANGUAGE: tsx
CODE:
```
import {getTranslations} from 'next-intl/server';

export default async function HomePage() {
  const t = await getTranslations('HomePage');
  return <h1>{t('title')}</h1>;
}
```

----------------------------------------

TITLE: Accessing Current Locale with useLocale and getLocale (TypeScript)
DESCRIPTION: This snippet demonstrates how to retrieve the current locale using `useLocale` for regular components (client-side) and `getLocale` for async Server Components. These functions are useful for implementing locale switchers or passing locale information to API calls.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_9

LANGUAGE: TSX
CODE:
```
// Regular components
import {useLocale} from 'next-intl';
const locale = useLocale();

// Async Server Components
import {getLocale} from 'next-intl/server';
const locale = await getLocale();
```

----------------------------------------

TITLE: Defining Request-Scoped i18n Configuration - TypeScript
DESCRIPTION: This file defines a request-scoped configuration for `next-intl` using `getRequestConfig`. It asynchronously determines the locale (e.g., 'en' statically, or from user settings/cookies) and loads the corresponding message file, making it available to Server Components.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/without-i18n-routing.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
import {getRequestConfig} from 'next-intl/server';

export default getRequestConfig(async () => {
  // Provide a static locale, fetch a user setting,
  // read from `cookies()`, `headers()`, etc.
  const locale = 'en';

  return {
    locale,
    messages: (await import(`../../messages/${locale}.json`)).default
  };
});
```

----------------------------------------

TITLE: Integrating Centralized Routing with `next-intl` Middleware (TypeScript)
DESCRIPTION: This example shows how to integrate the centralized `routing` configuration, previously defined using `defineRouting`, into `next-intl`'s middleware. By importing `createMiddleware` and the `routing` object, it simplifies the setup of internationalized routing for Next.js applications.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-3-22.mdx#_snippet_1

LANGUAGE: TypeScript
CODE:
```
import createMiddleware from 'next-intl/middleware';
import {routing} from './i18n/routing';

export default createMiddleware(routing);

// ...
```

----------------------------------------

TITLE: Providing Filtered Messages to `NextIntlClientProvider` in Next.js
DESCRIPTION: Illustrates how to fetch messages using `getMessages` in an async Server Component and then selectively provide a subset of these messages (e.g., 'Navigation') to client components via `NextIntlClientProvider` using `lodash/pick` for optimization.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_19

LANGUAGE: tsx
CODE:
```
import {NextIntlClientProvider} from 'next-intl';
import {getMessages} from 'next-intl/server';
import pick from 'lodash/pick';

async function Component({children}) {
  // Read messages configured via `i18n/request.ts`
  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={pick(messages, ['Navigation'])}>
      ...
    </NextIntlClientProvider>
  );
}
```

----------------------------------------

TITLE: Setting Request Locale in Individual Page for Static Rendering (TypeScript)
DESCRIPTION: This snippet illustrates how to apply `setRequestLocale` within an individual page (`app/[locale]/page.tsx`) to enable static rendering. It extracts the `locale` from `params` and sets it using `setRequestLocale` before any `next-intl` hooks like `useTranslations` are invoked. This ensures that the page can be statically rendered while still utilizing `next-intl`'s translation capabilities.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/with-i18n-routing.mdx#_snippet_15

LANGUAGE: tsx
CODE:
```
import {use} from 'react';
import {setRequestLocale} from 'next-intl/server';
import {useTranslations} from 'next-intl';

export default function IndexPage({params}) {
  const {locale} = use(params);

  // Enable static rendering
  setRequestLocale(locale);

  // Once the request locale is set, you
  // can call hooks from `next-intl`
  const t = useTranslations('IndexPage');

  return (
    // ...
  );
}
```

----------------------------------------

TITLE: Applying Error Handling with NextIntlClientProvider (Client-side)
DESCRIPTION: Applies custom error handling functions (`onError` and `getMessageFallback`) to client components using `NextIntlClientProvider`. This ensures consistent error reporting and fallback messages for translation issues on the client side.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_32

LANGUAGE: tsx
CODE:
```
import {NextIntlClientProvider, IntlErrorCode} from 'next-intl';

function onError(error) {
  if (error.code === IntlErrorCode.MISSING_MESSAGE) {
    // Missing translations are expected and should only log an error
    console.error(error);
  } else {
    // Other errors indicate a bug in the app and should be reported
    reportToErrorTracking(error);
  }
}

function getMessageFallback({namespace, key, error}) {
  const path = [namespace, key].filter((part) => part != null).join('.');

  if (error.code === IntlErrorCode.MISSING_MESSAGE) {
    return path + ' is not yet translated';
  } else {
    return 'Dear developer, please fix this message: ' + path;
  }
}

<NextIntlClientProvider
  onError={onError}
  getMessageFallback={getMessageFallback}
>
  ...
</NextIntlClientProvider>;
```

----------------------------------------

TITLE: Defining Centralized Routing Configuration with `defineRouting` (TypeScript)
DESCRIPTION: This snippet demonstrates how to use `defineRouting` to centralize and type-safely configure routing for `next-intl`. It includes settings for `locales`, `defaultLocale`, `localePrefix` with `mode` and `prefixes`, and `pathnames` for different locales, ensuring consistency across the application.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-3-22.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import {defineRouting} from 'next-intl/routing';

export const routing = defineRouting({
  locales: ['en-US', 'en-GB'],
  defaultLocale: 'en-US',
  localePrefix: {
    mode: 'always',
    prefixes: {
      'en-US': '/us',
      'en-GB': '/uk'
    }
  },
  pathnames: {
    '/': '/',
    '/organization': {
      'en-US': '/organization',
      'en-GB': '/organisation'
    }
  }
});
```

----------------------------------------

TITLE: Setting up next-intl Request Configuration for Server Components
DESCRIPTION: This snippet from `src/i18n/request.ts` configures 'next-intl' for Server Components. It uses `getRequestConfig` to determine the current locale based on the request and dynamically loads the corresponding message file, falling back to a default locale if the requested one is invalid.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/with-i18n-routing.mdx#_snippet_8

LANGUAGE: tsx
CODE:
```
import {getRequestConfig} from 'next-intl/server';
import {hasLocale} from 'next-intl';
import {routing} from './routing';

export default getRequestConfig(async ({requestLocale}) => {
  // Typically corresponds to the `[locale]` segment
  const requested = await requestLocale;
  const locale = hasLocale(routing.locales, requested)
    ? requested
    : routing.defaultLocale;

  return {
    locale,
    messages: (await import(`../../messages/${locale}.json`)).default
  };
});
```

----------------------------------------

TITLE: Configuring NextIntlClientProvider for Static Rendering with App Router
DESCRIPTION: This example demonstrates how to configure `NextIntlClientProvider` within a Server Component to ensure static rendering when using the App Router. Explicitly providing `timeZone`, `now`, and `locale` props prevents the component from opting into dynamic rendering by default, which is crucial for static site generation.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-3-0.mdx#_snippet_3

LANGUAGE: tsx
CODE:
```
<NextIntlClientProvider
  messages={messages}
  // By providing these props explicitly,
  // the provider can render statically.
  timeZone="Europe/Vienna"
  now={new Date()}
  locale={locale}
>
  ...
</NextIntlClientProvider>
```

----------------------------------------

TITLE: Implementing Internationalization in a Next.js Shared Component with next-intl
DESCRIPTION: This snippet demonstrates a `UserDetails` component using `next-intl`'s `useTranslations` hook. It's designed as a shared component, meaning it can execute as either a Server Component (default) or a Client Component depending on its import location. The `useTranslations` hook provides localized strings for the 'UserProfile' namespace, displaying a title and a dynamic follower count.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/server-client-components.mdx#_snippet_2

LANGUAGE: TypeScript
CODE:
```
import {useTranslations} from 'next-intl';

export default function UserDetails({user}) {
  const t = useTranslations('UserProfile');

  // This component will execute as a Server Component by default.
  // However, if it is imported from a Client Component, it will
  // execute as a Client Component.
  return (
    <section>
      <h2>{t('title')}</h2>
      <p>{t('followers', {count: user.numFollowers})}</p>
    </section>
  );
}
```

----------------------------------------

TITLE: Avoiding Hydration Mismatches with `useNow` (TSX)
DESCRIPTION: Illustrates using `suppressHydrationWarning` with `useNow` in components rendered on both server and client without a global `now` value. This prevents hydration errors for dynamically updated relative times, acknowledging expected client-side updates.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/dates-times.mdx#_snippet_5

LANGUAGE: tsx
CODE:
```
import {useNow, useFormatter} from 'next-intl';

function FormattedDate({date}) {
  const now = useNow();
  const format = useFormatter();

  return <span suppressHydrationWarning>{format.relativeTime(date, now)}</span>;
}
```

----------------------------------------

TITLE: Internationalizing Metadata with `generateMetadata` in Next.js (TypeScript)
DESCRIPTION: This snippet demonstrates how to localize page titles using the `generateMetadata` function in Next.js. It fetches translations based on the current locale and applies them to the `title` property of the metadata, enabling static rendering when an explicit locale is passed.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/actions-metadata-route-handlers.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import {getTranslations} from 'next-intl/server';

export async function generateMetadata({params}) {
  const {locale} = await params;
  const t = await getTranslations({locale, namespace: 'Metadata'});

  return {
    title: t('title')
  };
}
```

----------------------------------------

TITLE: Implementing Rich Text Reuse with `useTranslations` in next-intl (Alternative)
DESCRIPTION: This TypeScript/React snippet demonstrates an alternative approach for handling rich text elements in `next-intl` messages, replacing the deprecated `defaultTranslationValues`. It uses the `useTranslations` hook and a custom `RichText` component to pass tags for rich text rendering, ensuring type safety and serialization across RSC boundaries.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-3-22.mdx#_snippet_10

LANGUAGE: tsx
CODE:
```
import {useTranslations} from 'next-intl';
import RichText from '@/components/RichText';

function AboutPage() {
  const t = useTranslations('AboutPage');
  return <RichText>{(tags) => t.rich('description', tags)}</RichText>;
}
```

----------------------------------------

TITLE: Generating Open Graph Images with next-intl in Next.js
DESCRIPTION: This snippet demonstrates how to programmatically generate Open Graph images using `next-intl` within a Next.js application. It fetches translations based on the current locale from the URL parameters and renders a localized title within the image response. This approach ensures that Open Graph images are internationalized.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/actions-metadata-route-handlers.mdx#_snippet_3

LANGUAGE: TSX
CODE:
```
import {ImageResponse} from 'next/og';
import {getTranslations} from 'next-intl/server';

export default async function OpenGraphImage({params}) {
  const {locale} = await params;
  const t = await getTranslations({locale, namespace: 'OpenGraphImage'});
  return new ImageResponse(<div style={{fontSize: 128}}>{t('title')}</div>);
}
```

----------------------------------------

TITLE: Implementing Localized Pathnames Navigation - next-intl
DESCRIPTION: This example shows how to configure localized pathnames using `createLocalizedPathnamesNavigation` from `next-intl`. It defines a `pathnames` object to map internal paths to locale-specific external paths, enabling flexible routing for internationalized applications. This approach allows for upgrading from shared to localized pathnames by replacing the factory function.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-3-0.mdx#_snippet_1

LANGUAGE: TypeScript
CODE:
```
import {
  createLocalizedPathnamesNavigation,
  Pathnames
} from 'next-intl/navigation';

export const locales = ['en', 'de'] as const;

// The `pathnames` object holds pairs of internal
// and external paths, separated by locale.
export const pathnames = {
  // If all locales use the same pathname, a
  // single external path can be provided.
  '/': '/',
  '/blog': '/blog',

  // If locales use different paths, you can
  // specify each external path per locale.
  '/about': {
    en: '/about',
    de: '/ueber-uns'
  }
} satisfies Pathnames<typeof locales>;

export const {Link, redirect, usePathname, useRouter} =
  createLocalizedPathnamesNavigation({locales, pathnames});
```

----------------------------------------

TITLE: Using useRouter for Programmatic Navigation (next-intl, TSX)
DESCRIPTION: Demonstrates how to use the `useRouter` hook from `next-intl` for programmatic navigation within a client component. It shows examples of pushing a new route by pathname, pushing with search parameters using the `query` option, and replacing the current route while overriding the locale. Requires importing `useRouter` from the `next-intl` navigation module.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_8

LANGUAGE: TSX
CODE:
```
'use client';

import {useRouter} from '@/i18n/navigation';

const router = useRouter();

// When the user is on `/en`, the router will navigate to `/en/about`
router.push('/about');

// Search params can be added via `query`
router.push({
  pathname: '/users',
  query: {sortBy: 'name'}
});

// You can override the `locale` to switch to another language
router.replace('/about', {locale: 'de'});
```

----------------------------------------

TITLE: Setting Up IntlProvider and Using Translations
DESCRIPTION: This example demonstrates the core setup for `use-intl` by wrapping the application with `IntlProvider`. It shows how to pass a `messages` object and `locale` to the provider. Within a child component (`App`), the `useTranslations` hook is then used to access and display a localized greeting, illustrating the end-to-end flow of internationalization.
SOURCE: https://github.com/amannn/next-intl/blob/main/packages/use-intl/README.md#_snippet_2

LANGUAGE: jsx
CODE:
```
import {IntlProvider, useTranslations} from 'use-intl';

// You can get the messages from anywhere you like. You can also
// fetch them from within a component and then render the provider
// along with your app once you have the messages.
const messages = {
  App: {
    hello: 'Hello {firstName}!'
  }
};

function Root() {
  return (
    <IntlProvider messages={messages} locale="en">
      <App user={{firstName: 'Jane'}} />
    </IntlProvider>
  );
}

function App({user}) {
  const t = useTranslations('App');
  return <h1>{t('hello', {firstName: user.firstName})}</h1>;
}
```

----------------------------------------

TITLE: Defining Type-Safe Message Arguments in JSON
DESCRIPTION: This JSON snippet defines a message key 'UserProfile.title' that expects a 'firstName' argument. This structure allows `next-intl` to enforce type safety, ensuring that calls to this message key provide the required argument.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/typescript.mdx#_snippet_9

LANGUAGE: json
CODE:
```
{
  "UserProfile": {
    "title": "Hello {firstName}"
  }
}
```

----------------------------------------

TITLE: Creating Server-Side Date Instance in Next.js Page Component (TypeScript)
DESCRIPTION: This snippet demonstrates how to instantiate a `Date` object (`now`) directly within a Next.js Server Component (a default page component). This ensures the `Date` is created only on the server, and then passed as a serializable prop to a client or server child component, avoiding client-side re-creation and potential hydration issues.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/date-formatting-nextjs.mdx#_snippet_2

LANGUAGE: TypeScript
CODE:
```
import BlogPostPublishedDate from './BlogPostPublishedDate';

export default function BlogPostPage() {
  // ✅ Is only called on the server
  const now = new Date();

  const published = ...;

  return <BlogPostPublishedDate now={now} published={published} />;
}
```

----------------------------------------

TITLE: Defining English Messages in JSON
DESCRIPTION: This JSON file defines the English translations for the application, providing key-value pairs for different UI elements and pages. It serves as a local data source for internationalized content, typically located in the `messages` directory.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/with-i18n-routing.mdx#_snippet_1

LANGUAGE: json
CODE:
```
{
  "HomePage": {
    "title": "Hello world!",
    "about": "Go to the about page"
  }
}
```

----------------------------------------

TITLE: Formatting Dates and Times with `useFormatter` in Next.js (JavaScript)
DESCRIPTION: This snippet demonstrates how to use the `dateTime` function from `next-intl`'s `useFormatter` hook to format a `Date` object. It shows how to specify formatting options like year, month, day, hour, and minute, similar to `Intl.DateTimeFormat`.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/dates-times.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import {useFormatter} from 'next-intl';

function Component() {
  const format = useFormatter();
  const dateTime = new Date('2020-11-20T10:36:01.516Z');

  // Renders "Nov 20, 2020"
  format.dateTime(dateTime, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });

  // Renders "11:36 AM"
  format.dateTime(dateTime, {hour: 'numeric', minute: 'numeric'});
}
```

----------------------------------------

TITLE: Augmenting next-intl AppConfig for Locale, Messages, and Formats (TypeScript)
DESCRIPTION: This snippet demonstrates how to augment the `AppConfig` interface within the `next-intl` module to provide type definitions for `Locale`, `Messages`, and `Formats`. This enhances autocompletion and type safety across the application by linking to routing locales, message files, and format definitions.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/typescript.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import {routing} from '@/i18n/routing';
import {formats} from '@/i18n/request';
import messages from './messages/en.json';

declare module 'next-intl' {
  interface AppConfig {
    Locale: (typeof routing.locales)[number];
    Messages: typeof messages;
    Formats: typeof formats;
  }
}
```

----------------------------------------

TITLE: Configure next-intl Middleware with localePrefix 'never' (TSX)
DESCRIPTION: Configures the `next-intl` routing middleware to never include a locale prefix in the user-facing URL. This is useful for domain-based routing or cookie-based locale determination with static rendering. Requires pages to still be within a `[locale]` folder and the middleware matcher to handle unprefixed paths.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_3

LANGUAGE: tsx
CODE:
```
import {defineRouting} from 'next-intl/routing';

export const routing = defineRouting({
  // ...
  localePrefix: 'never'
});
```

----------------------------------------

TITLE: Revalidating Localized Pathnames in Next.js
DESCRIPTION: Demonstrates how to use `revalidatePath` in Next.js to revalidate pages with localized pathnames. It shows examples for both statically generated and dynamically generated routes, highlighting the difference in the path used for revalidation.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_7

LANGUAGE: tsx
CODE:
```
// Statically generated at build time
revalidatePath('/de/news/some-article');

// Dynamically generated at runtime:
revalidatePath('/de/neuigkeiten/some-article');
```

----------------------------------------

TITLE: Formatting Dates in Components with next-intl/useFormatter (TypeScript)
DESCRIPTION: This snippet illustrates how to use the `useFormatter` hook from `next-intl` within a React component to format dates consistently across server and client environments. It shows examples of formatting a date as a full date string (`dateTime`) and as a relative time string (`relativeTime`), leveraging the centralized configuration from `i18n/request.ts`.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/date-formatting-nextjs.mdx#_snippet_10

LANGUAGE: TypeScript
CODE:
```
import {useFormatter} from 'next-intl';

type Props = {
  published: Date;
};

export default function BlogPostPublishedDate({published}: Props) {
  // ✅ Works in any environment
  const format = useFormatter();

  // "Sep 25, 2024"
  format.dateTime(published);

  // "8 days ago"
  format.relativeTime(published);
}
```

----------------------------------------

TITLE: Formatting Plain Numbers with next-intl's useFormatter Hook (JavaScript)
DESCRIPTION: This snippet demonstrates how to format plain numbers using the `useFormatter` hook from `next-intl`. It shows an example of formatting a number as currency, leveraging `Intl.NumberFormat` options to control style and currency type. This is suitable for numbers not embedded in message strings.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/numbers.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import {useFormatter} from 'next-intl';

function Component() {
  const format = useFormatter();

  // Renders "$499.90"
  format.number(499.9, {style: 'currency', currency: 'USD'});
}
```

----------------------------------------

TITLE: Accessing a Static Message with useTranslations - JavaScript
DESCRIPTION: This JavaScript snippet demonstrates how to retrieve a static message using the `t` function (from `useTranslations`). It shows the basic usage for fetching a message without any parameters.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_11

LANGUAGE: javascript
CODE:
```
t('message'); // "Hello world!"
```

----------------------------------------

TITLE: Accessing Messages with `useMessages` and `getMessages` in Next.js
DESCRIPTION: Demonstrates how to retrieve internationalization messages using `useMessages` for regular client components and `getMessages` for async server components from the `next-intl` library. `useMessages` is a hook, while `getMessages` is an async function.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_18

LANGUAGE: tsx
CODE:
```
// Regular components
import {useMessages} from 'next-intl';
const messages = useMessages();

// Async Server Components
import {getMessages} from 'next-intl/server';
const messages = await getMessages();
```

----------------------------------------

TITLE: Creating a Localized 404 Page with `not-found.tsx` in Next.js
DESCRIPTION: This snippet demonstrates how to create a localized 404 page using `next-intl`'s `useTranslations` hook within `app/[locale]/not-found.tsx`. It provides a translated title for the not-found page, rendered when the `notFound` function is explicitly called within a route.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/error-files.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import {useTranslations} from 'next-intl';

export default function NotFoundPage() {
  const t = useTranslations('NotFoundPage');
  return <h1>{t('title')}</h1>;
}
```

----------------------------------------

TITLE: Configuring next-intl Plugin - TypeScript
DESCRIPTION: This configuration sets up the `next-intl` plugin in `next.config.ts`. It imports `createNextIntlPlugin` and wraps the existing Next.js configuration, enabling `next-intl` to create an alias for request-specific i18n configuration in Server Components.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/without-i18n-routing.mdx#_snippet_2

LANGUAGE: js
CODE:
```
import {NextConfig} from 'next';
import createNextIntlPlugin from 'next-intl/plugin';

const nextConfig: NextConfig = {};

const withNextIntl = createNextIntlPlugin();
export default withNextIntl(nextConfig);
```

----------------------------------------

TITLE: Accessing Message with Dynamic Interpolation - JavaScript
DESCRIPTION: This JavaScript snippet shows how to use the `t` function to retrieve a message that requires dynamic value interpolation. The `name` parameter is passed as an object to replace the placeholder in the message string.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_13

LANGUAGE: javascript
CODE:
```
t('message', {name: 'Jane'}); // "Hello Jane!"
```

----------------------------------------

TITLE: Using `getTranslations` for Server-Side Translations (Next.js API Routes)
DESCRIPTION: Demonstrates how to fetch translations on the server-side using `getTranslations` from `next-intl/server`. This function is suitable for Next.js API routes, Route Handlers, or the Metadata API, allowing translation consumption outside of React components by providing the locale as a parameter.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/translations-outside-of-react-components.mdx#_snippet_3

LANGUAGE: tsx
CODE:
```
import {getTranslations} from 'next-intl/server';

// The `locale` is received from Next.js via `params`
const locale = params.locale;

// This creates the same function that is returned by `useTranslations`.
const t = await getTranslations({locale});

// Result: "Hello world!"
t('hello', {name: 'world'});
```

----------------------------------------

TITLE: Configuring Middleware Matcher in Next.js
DESCRIPTION: This configuration defines the paths for which the `next-intl` middleware should be invoked. It uses regular expressions to match all pathnames except those starting with `/api`, `/_next`, `/_vercel`, or containing a dot (typically static files), while explicitly including paths within `/users`.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/middleware.mdx#_snippet_1

LANGUAGE: TypeScript
CODE:
```
export const config = {
  // Matcher entries are linked with a logical "or", therefore
  // if one of them matches, the middleware will be invoked.
  matcher: [
    // Match all pathnames except for
    // - … if they start with `/api`, `/_next` or `/_vercel`
    // - … the ones containing a dot (e.g. `favicon.ico`)
    '/((?!api|_next|_vercel|.*\\..*).*)/',

    // However, match all pathnames within `/users`, optionally with a locale prefix
    '/([\\w-]+)?/users/(.+)'
  ]
};
```

----------------------------------------

TITLE: Configuring next-intl Plugin in Next.js (JavaScript)
DESCRIPTION: This configuration sets up the `next-intl` plugin in `next.config.js`, creating an alias for request-specific i18n configuration, essential for Server Components. It uses `require` to import the plugin and exports the modified Next.js configuration.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/with-i18n-routing.mdx#_snippet_3

LANGUAGE: js
CODE:
```
const createNextIntlPlugin = require('next-intl/plugin');

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig = {};

module.exports = withNextIntl(nextConfig);
```

----------------------------------------

TITLE: Referencing Global Number Formats in next-intl (JavaScript)
DESCRIPTION: This code illustrates how to apply globally configured number formats using the `useFormatter` hook by referencing their names. It also shows how to optionally override specific formatting options, such as currency, when applying a global format.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/numbers.mdx#_snippet_1

LANGUAGE: JavaScript
CODE:
```
// Use a global format
format.number(499.9, 'precise');

// Optionally override some options
format.number(499.9, 'price', {currency: 'USD'});
```

----------------------------------------

TITLE: Using Global Date Formats with `dateTime` in Next.js (JavaScript)
DESCRIPTION: This example illustrates how to apply pre-configured global date formats using the `dateTime` function by passing the format's name as the second argument. It also shows how to optionally override specific formatting options when using a global format.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/dates-times.mdx#_snippet_1

LANGUAGE: JavaScript
CODE:
```
// Use a global format
format.dateTime(dateTime, 'short');

// Optionally override some options
format.dateTime(dateTime, 'short', {year: 'numeric'});
```

----------------------------------------

TITLE: Defining a Message with Dynamic Interpolation - JSON
DESCRIPTION: This JSON snippet defines a message key 'message' that includes a placeholder `{name}` for dynamic value interpolation. This allows for personalized messages by inserting variable data.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_12

LANGUAGE: json
CODE:
```
"message": "Hello {name}!"
```

----------------------------------------

TITLE: Composing next-intl Middleware with Custom Logic
DESCRIPTION: This example demonstrates how to integrate `next-intl`'s middleware with custom logic. It shows how to pre-process the incoming `NextRequest`, call the `next-intl` middleware, and then post-process the `NextResponse` before returning it, allowing for dynamic locale detection or header modification.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/middleware.mdx#_snippet_3

LANGUAGE: TypeScript
CODE:
```
import createMiddleware from 'next-intl/middleware';
import {NextRequest} from 'next/server';

export default async function middleware(request: NextRequest) {
  // Step 1: Use the incoming request (example)
  const defaultLocale = request.headers.get('x-your-custom-locale') || 'en';

  // Step 2: Create and call the next-intl middleware (example)
  const handleI18nRouting = createMiddleware({
    locales: ['en', 'de'],
    defaultLocale
  });
  const response = handleI18nRouting(request);

  // Step 3: Alter the response (example)
  response.headers.set('x-your-custom-locale', defaultLocale);

  return response;
}

export const config = {
  // Match only internationalized pathnames
  matcher: ['/', '/(de|en)/:path*']
};
```

----------------------------------------

TITLE: Configuring Error Handling in next-intl (Server-side)
DESCRIPTION: Configures custom error handling for `next-intl` on the server side using `getRequestConfig`. It defines `onError` to log missing messages and report other errors, and `getMessageFallback` to provide user-friendly fallback strings.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_31

LANGUAGE: tsx
CODE:
```
import {getRequestConfig} from 'next-intl/server';
import {IntlErrorCode} from 'next-intl';

export default getRequestConfig(async () => {
  return {
    onError(error) {
      if (error.code === IntlErrorCode.MISSING_MESSAGE) {
        // Missing translations are expected and should only log an error
        console.error(error);
      } else {
        // Other errors indicate a bug in the app and should be reported
        reportToErrorTracking(error);
      }
    },

    getMessageFallback({namespace, key, error}) {
      const path = [namespace, key].filter((part) => part != null).join('.');

      if (error.code === IntlErrorCode.MISSING_MESSAGE) {
        return path + ' is not yet translated';
      } else {
        return 'Dear developer, please fix this message: ' + path;
      }
    }

    // ...
  };
});
```

----------------------------------------

TITLE: Providing next-intl Messages in Next.js RootLayout (TSX)
DESCRIPTION: This code demonstrates how to provide internationalized messages to client components, specifically for the `error.js` file, using `NextIntlClientProvider` in the Next.js root layout. It fetches all messages using `getMessages` and then uses `lodash/pick` to selectively provide only the 'Error' messages to the client provider, optimizing the bundle size. This is crucial because `error.js` must be a Client Component.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/error-files.mdx#_snippet_6

LANGUAGE: TSX
CODE:
```
import pick from 'lodash/pick';
import {NextIntlClientProvider} from 'next-intl';
import {getMessages} from 'next-intl/server';

export default async function RootLayout(/* ... */) {
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider
          locale={locale}
          // Make sure to provide at least the messages for `Error`
          messages={pick(messages, 'Error')}
        >
          {children}
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
```

----------------------------------------

TITLE: Naive Off-Component Translation for Error Messages (Next.intl)
DESCRIPTION: Illustrates a problematic approach where translation formatting occurs outside a React component, specifically within an asynchronous utility function. This method can lead to stale messages if the language changes or if dynamic values (like countdowns) need real-time updates, as the message is formatted once and returned.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/translations-outside-of-react-components.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
import {useTranslations, useNow} from 'next-intl';
import {addMinutes} from 'date-fns';

function sendFeedback() {
  // ❌ Bad implementation: Returns formatted messages
  API.sendFeedback().catch((error) => {
    // In case of a gateway timeout, notify the
    // user that we'll try again in 5 minutes
    if (error.status === 504) {
      // (let's assume `t` is defined here for the sake of the example)
      return t('timeout', {nextAttempt: addMinutes(new Date(), 5)});
    }
  });
}

function FeedbackForm({user}) {
  const t = useTranslations('Form');
  const [errorMessage, setErrorMessage] = useState();

  function onSubmit() {
    sendFeedback().catch((errorMessage) => {
      setErrorMessage(errorMessage);
    });
  }

  return (
    <form onSubmit={onSubmit}>
      {errorMessage != null && <p>{errorMessage}</p>}
      ...
    </form>
  );
}
```

----------------------------------------

TITLE: Handling Dynamic Params with Link (TSX)
DESCRIPTION: Shows two ways to handle dynamic route parameters when using the `Link` component, depending on whether the `pathnames` setting is enabled in the configuration. It illustrates passing a final string or an object with `pathname` and `params`.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_3

LANGUAGE: tsx
CODE:
```
// 1. A final string (when not using `pathnames`)
<Link href="/users/12">Susan</Link>

// 2. An object (when using `pathnames`)
<Link href={{
  pathname: '/users/[userId]',
  params: {userId: '5'}
}}>
  Susan
</Link>
```

----------------------------------------

TITLE: Handling Dynamic Pathname Construction with next-intl (TypeScript)
DESCRIPTION: Demonstrates how to handle dynamic path segments when constructing a localized pathname using `getPathname`. It shows the two methods for passing dynamic parameters: a final string (without `pathnames`) and an object with `pathname` and `params` (with `pathnames`).
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_19

LANGUAGE: TypeScript
CODE:
```
// 1. A final string (when not using `pathnames`)
const pathname = getPathname({
  locale: 'en',
  href: '/users/12'
});

// 2. An object (when using `pathnames`)
const pathname = getPathname({
  locale: 'en',
  href: {
    pathname: '/users/[userId]',
    params: {userId: '5'}
  }
});
```

----------------------------------------

TITLE: Using `useTranslations` Hook in Next.js App Router (TSX)
DESCRIPTION: This snippet demonstrates how to use the `useTranslations` hook from `next-intl` in a client-side or synchronous React component within the Next.js App Router. It initializes a translation function `t` for the 'HomePage' namespace, which is then used to display a translated title. This approach is suitable for components that do not require asynchronous data fetching for translations.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/without-i18n-routing.mdx#_snippet_7

LANGUAGE: tsx
CODE:
```
import {useTranslations} from 'next-intl';

export default function HomePage() {
  const t = useTranslations('HomePage');
  return <h1>{t('title')}</h1>;
}
```

----------------------------------------

TITLE: Retrieving Current Locale in Components (TypeScript)
DESCRIPTION: This snippet provides examples of how to retrieve the current locale in both regular and async components. This is a prerequisite for the updated `redirect` function, which now requires an explicit locale argument.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-3-22.mdx#_snippet_6

LANGUAGE: tsx
CODE:
```
// Retrieving the current locale
// ... in regular components:
const locale = useLocale();
// ... in async components:
const locale = await getLocale();
```

----------------------------------------

TITLE: Adjusting Component Layout for RTL Languages in Next.js (TSX)
DESCRIPTION: Shows how to conditionally render UI elements, like navigation arrows, based on the detected text direction using `rtl-detect`. This ensures proper layout mirroring within components for right-to-left languages, enhancing user experience.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_36

LANGUAGE: tsx
CODE:
```
import {useTranslations} from 'next-intl';
import {getLangDir} from 'rtl-detect';

export default function Breadcrumbs({children, params}) {
  const t = useTranslations('Breadcrumbs');
  const locale = useLocale();
  const direction = getLangDir(locale);

  return (
    <div style={{display: 'flex'}}>
      <p>{t('home')}</p>
      <div style={{marginInlineStart: 10}}>
        {direction === 'ltr' ? <ArrowRight /> : <ArrowLeft />}
      </div>
      <p style={{marginInlineStart: 10}}>{t('about')}</p>
    </div>
  );
}
```

----------------------------------------

TITLE: Configuring NextIntlClientProvider as a Global Storybook Decorator (TypeScript)
DESCRIPTION: This TypeScript snippet illustrates how to manually configure Storybook by defining a global decorator in `.storybook/preview.tsx`. It wraps all stories with `NextIntlClientProvider`, passing a default locale ('en') and messages from `defaultMessages`. This setup ensures that components relying on `next-intl`'s hook-based APIs, such as `useTranslations`, receive the necessary internationalization context when rendered in isolation within Storybook.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/storybook.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import {Preview} from '@storybook/react';
import defaultMessages from '../messages/en.json';

const preview: Preview = {
  decorators: [
    (Story) => (
      <NextIntlClientProvider
        locale="en"
        messages={defaultMessages}
        // ... potentially other config
      >
        <Story />
      </NextIntlClientProvider>
    )
  ]
};

export default preview;
```

----------------------------------------

TITLE: Configuring next-intl to Generate Message Declarations (Next.js)
DESCRIPTION: This `next.config.mjs` snippet configures the `next-intl` plugin to generate a `.d.json.ts` declaration file for the specified message file (`./messages/en.json`). This generated file provides exact types for JSON messages, enabling comprehensive type safety for message arguments when integrated with `AppConfig`.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/typescript.mdx#_snippet_12

LANGUAGE: tsx
CODE:
```
import {createNextIntlPlugin} from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin({
  experimental: {
    // Provide the path to the messages that you're using in `AppConfig`
    createMessagesDeclaration: './messages/en.json'
  }
  // ...
});

// ...
```

----------------------------------------

TITLE: Formatting Rich Text with Custom Tags (next-intl, JavaScript)
DESCRIPTION: This snippet demonstrates how to use `t.rich` to map custom tags defined in your translation messages (e.g., `<guidelines>`) to specific React components (e.g., `<a>`). This allows for dynamic rich text formatting within your application.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_23

LANGUAGE: json
CODE:
```
{
  "message": "Please refer to <guidelines>the guidelines</guidelines>."
}
```

LANGUAGE: javascript
CODE:
```
// Returns `<>Please refer to <a href="/guidelines">the guidelines</a>.</>`
t.rich('message', {
  guidelines: (chunks) => <a href="/guidelines">{chunks}</a>
});
```

----------------------------------------

TITLE: Configuring 'as-needed' Locale Prefix in next-intl Routing (TSX)
DESCRIPTION: Sets the `localePrefix` option to `'as-needed'` within the `defineRouting` configuration. This removes the locale prefix for the default locale while keeping it for others (e.g., `/about` for default, `/de/about` for German). Requires careful middleware configuration.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_2

LANGUAGE: tsx
CODE:
```
import {defineRouting} from 'next-intl/routing';

export const routing = defineRouting({
  // ...
  localePrefix: 'as-needed'
});
```

----------------------------------------

TITLE: Passing Translated Labels to Client Components in Next.js (FAQEntry)
DESCRIPTION: This Server Component demonstrates how to fetch translations using `useTranslations` from `next-intl` and then pass the translated content (`title` and `description`) as props or children to a Client Component (`Expandable` and `FAQContent`), allowing interactive features on translated content while keeping translation logic on the server.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/server-client-components.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
import {useTranslations} from 'next-intl';
import Expandable from './Expandable'; // A Client Component
import FAQContent from './FAQContent';

export default function FAQEntry() {
  // Call `useTranslations` in a Server Component ...
  const t = useTranslations('FAQEntry');

  // ... and pass translated content to a Client Component
  return (
    <Expandable title={t('title')}>
      <FAQContent content={t('description')} />
    </Expandable>
  );
}
```

----------------------------------------

TITLE: Formatting Translations During React Render (Next.intl)
DESCRIPTION: Presents an improved pattern for handling internationalized messages by storing raw data structures in state and performing translation formatting during the React rendering phase. This ensures messages are always up-to-date with dynamic values and respond correctly to language changes, enhancing user experience.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/translations-outside-of-react-components.mdx#_snippet_2

LANGUAGE: tsx
CODE:
```
import {useTranslations, useNow} from 'next-intl';
import {addMinutes} from 'date-fns';

function FeedbackForm({user}) {
  const t = useTranslations('Form');
  const [retry, setRetry] = useState();
  const now = useNow({
    // Update every minute
    updateInterval: 1000 * 60
  });

  function onSubmit() {
    // ✅ Good implementation: Store data structures in state
    API.sendFeedback().catch((error) => {
      if (error.status === 504) {
        setRetry(addMinutes(now, 5));
      }
    });
  }

  return (
    <form onSubmit={onSubmit}>
      {retry != null && <p>{t('timeout', {nextAttempt: nextAttempt - now})}</p>}
      ...
    </form>
  );
}
```

----------------------------------------

TITLE: Defining Client-Side Error Handling Provider for NextIntlClientProvider (TSX)
DESCRIPTION: This client component defines a provider (`IntlErrorHandlingProvider`) to pass non-serializable props like `onError` and `getMessageFallback` to `NextIntlClientProvider`. These functions cannot be inherited directly from Server Components due to React's serialization limitations.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
'use client';

import {NextIntlClientProvider} from 'next-intl';

export default function IntlErrorHandlingProvider({children}) {
  return (
    <NextIntlClientProvider
      onError={(error) => console.error(error)}
      getMessageFallback={({namespace, key}) => `${namespace}.${key}`}
    >
      {children}
    </NextIntlClientProvider>
  );
}
```

----------------------------------------

TITLE: Applying Custom Number Formats with next-intl's `t` Function (JavaScript)
DESCRIPTION: This JavaScript snippet demonstrates how to apply custom number formatting options directly when calling the `t` (translation) function. It shows how to specify the style and currency for a number placeholder within a message, overriding default or global settings for that specific translation.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/numbers.mdx#_snippet_4

LANGUAGE: JavaScript
CODE:
```
t(
  'price',
  {price: 32000.99},
  {
    number: {
      currency: {
        style: 'currency',
        currency: 'EUR'
      }
    }
  }
);
```

----------------------------------------

TITLE: Correcting Number Formatting in ICU Messages (JSON)
DESCRIPTION: This JSON snippet illustrates the difference between an unformatted ICU message and one that correctly applies number formatting. It demonstrates how adding `{count, number}` ensures that the `count` argument is formatted according to locale-specific number conventions (e.g., with thousands separators).
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-4-0.mdx#_snippet_6

LANGUAGE: json
CODE:
```
// ✖️ Would be: "30000 followers"
"{count} followers"

// ✅ Valid: "30,000 followers"
"{count, number} followers"
```

----------------------------------------

TITLE: Registering Augmented Types for next-intl 4.0 (TSX)
DESCRIPTION: This snippet demonstrates how to register `Messages` and `Formats` types under a single `AppConfig` interface within the `next-intl` module. This centralizes type augmentation, improving type safety for internationalization messages and formats in Next.js applications. It requires importing `formats` and message definitions.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-4-0.mdx#_snippet_0

LANGUAGE: TSX
CODE:
```
// global.ts

import {formats} from '@/i18n/request';
import en from './messages/en.json';

declare module 'next-intl' {
  interface AppConfig {
    Messages: typeof en;
    Formats: typeof formats;
  }
}
```

----------------------------------------

TITLE: Configuring Global Time Zone in `i18n/request.ts` for Next.js
DESCRIPTION: Shows how to define a global time zone for `next-intl` applications within the `i18n/request.ts` configuration file. This time zone will be used for date and time formatting across the application, defaulting to the server's time zone if not specified.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_20

LANGUAGE: tsx
CODE:
```
import {getRequestConfig} from 'next-intl/server';

export default getRequestConfig(async () => {
  return {
    // The time zone can either be statically defined, read from the
    // user profile if you store such a setting, or based on dynamic
    // request information like the locale or a cookie.
    timeZone: 'Europe/Vienna'

    // ...
  };
});
```

----------------------------------------

TITLE: Using Type-Safe Message Arguments in a React Component (TSX)
DESCRIPTION: This TSX component demonstrates how to use `useTranslations` with type-safe message arguments. It highlights that calling `t('title')` without the required `firstName` argument results in a type error, while providing it correctly satisfies the type contract.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/typescript.mdx#_snippet_10

LANGUAGE: tsx
CODE:
```
function UserProfile({user}) {
  const t = useTranslations('UserProfile');

  // ✖️ Missing argument
  t('title');

  // ✅ Argument is provided
  t('title', {firstName: user.firstName});
}
```

----------------------------------------

TITLE: Integrating next-intl Middleware with Clerk (TypeScript)
DESCRIPTION: This example shows how to combine `next-intl` middleware with `@clerk/nextjs` middleware. The `clerkMiddleware` is executed first to handle protected routes and authentication, and then `handleI18nRouting` from `next-intl` processes the request for internationalization. This ensures that authentication checks precede i18n routing, providing a secure and localized application flow.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/middleware.mdx#_snippet_5

LANGUAGE: TypeScript
CODE:
```
import {clerkMiddleware, createRouteMatcher} from '@clerk/nextjs/server';
import createMiddleware from 'next-intl/middleware';
import {routing} from './i18n/routing';

const handleI18nRouting = createMiddleware(routing);

const isProtectedRoute = createRouteMatcher(['/:locale/dashboard(.*)']);

export default clerkMiddleware(async (auth, req) => {
  if (isProtectedRoute(req)) await auth.protect();

  return handleI18nRouting(req);
});

export const config = {
  // Match only internationalized pathnames
  matcher: ['/', '/(de|en)/:path*']
};
```

----------------------------------------

TITLE: Providing All Messages to Client Components in RootLayout with NextIntlClientProvider (TypeScript)
DESCRIPTION: This snippet demonstrates the default behavior of next-intl where all messages are made available to client components by wrapping the application's root layout with NextIntlClientProvider without explicitly passing a messages prop. This approach is suitable for highly dynamic applications where many client components require access to a broad range of translations, simplifying message management at the cost of potentially larger client bundles.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/server-client-components.mdx#_snippet_10

LANGUAGE: tsx
CODE:
```
import {NextIntlClientProvider} from 'next-intl';

export default async function RootLayout(/* ... */) {
  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider>...</NextIntlClientProvider>
      </body>
    </html>
  );
}
```

----------------------------------------

TITLE: Implementing next-intl Translations in Next.js error.tsx (TSX)
DESCRIPTION: This snippet shows a standard implementation of the `error.tsx` client component, utilizing `useTranslations` from `next-intl` to display localized error messages. It receives `error` and `reset` props from the Next.js error boundary, allowing it to display an error title and a retry button with translated text. The `'use client'` directive marks it as a Client Component.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/error-files.mdx#_snippet_7

LANGUAGE: TSX
CODE:
```
'use client';

import {useTranslations} from 'next-intl';

export default function Error({error, reset}) {
  const t = useTranslations('Error');

  return (
    <div>
      <h1>{t('title')}</h1>
      <button onClick={reset}>{t('retry')}</button>
    </div>
  );
}
```

----------------------------------------

TITLE: Integrating next-intl Middleware with Supabase Authentication (TypeScript)
DESCRIPTION: This snippet demonstrates how to combine `next-intl` middleware with Supabase authentication. The `handleI18nRouting` from `next-intl` is executed first to generate a response, which is then passed to the `updateSession` utility from Supabase. This ensures that internationalization routing occurs, and then Supabase handles session updates based on the `next-intl`'s response, maintaining both localization and authentication state.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/middleware.mdx#_snippet_7

LANGUAGE: TypeScript
CODE:
```
import createMiddleware from 'next-intl/middleware';
import {type NextRequest} from 'next/server';
import {routing} from './i18n/routing';
import {updateSession} from './utils/supabase/middleware';

const handleI18nRouting = createMiddleware(routing);

export async function middleware(request: NextRequest) {
  const response = handleI18nRouting(request);

  // A `response` can now be passed here
  return await updateSession(request, response);
}

export const config = {
  matcher: ['/', '/(de|en)/:path*']
};
```

----------------------------------------

TITLE: Defining Global Formats for next-intl (TypeScript)
DESCRIPTION: This TypeScript snippet defines `global formats` for `next-intl`, encompassing `dateTime`, `number`, and `list` formats. Exporting these formats is essential for enabling strict type validation when they are referenced in `format.dateTime`, `format.number`, and `format.list` calls throughout the application.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/typescript.mdx#_snippet_15

LANGUAGE: ts
CODE:
```
import {Formats} from 'next-intl';

export const formats = {
  dateTime: {
    short: {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    }
  },
  number: {
    precise: {
      maximumFractionDigits: 5
    }
  },
  list: {
    enumeration: {
      style: 'long',
      type: 'conjunction'
    }
  }
} satisfies Formats;

// ...
```

----------------------------------------

TITLE: Defining Basic Routing Configuration with next-intl (TSX)
DESCRIPTION: Configures the fundamental routing settings for `next-intl` using the `defineRouting` function. It specifies the list of supported locales and sets the default locale to be used when no locale matches the request.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import {defineRouting} from 'next-intl/routing';

export const routing = defineRouting({
  // A list of all locales that are supported
  locales: ['en', 'de'],

  // Used when no locale matches
  defaultLocale: 'en'
});
```

----------------------------------------

TITLE: Using Defined Global Formats with useFormatter (React Component)
DESCRIPTION: Demonstrates how to use the `useFormatter` hook from `next-intl` to apply globally defined `dateTime`, `number`, and `list` formats within a React component. It shows examples of formatting a date, a number, and a list.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_28

LANGUAGE: tsx
CODE:
```
import {useFormatter} from 'next-intl';

function Component() {
  const format = useFormatter();

  format.dateTime(new Date('2020-11-20T10:36:01.516Z'), 'short');
  format.number(47.*********, 'precise');
  format.list(['HTML', 'CSS', 'JavaScript'], 'enumeration');
}
```

----------------------------------------

TITLE: Applying Ordinal Pluralization in next-intl Messages (JSON)
DESCRIPTION: This snippet demonstrates how to use the `selectordinal` argument in `next-intl` messages to apply pluralization based on the order of items. It defines different forms ('st', 'nd', 'rd', 'th') for various ordinal categories (one, two, few, other) for the 'year' variable. This feature leverages `Intl.PluralRules` to determine the correct plural tag for a given number.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_16

LANGUAGE: json
CODE:
```
"message": "It's your {year, selectordinal, one {#st} two {#nd} few {#rd} other {#th}} birthday!"
```

----------------------------------------

TITLE: Formatting Date for Display with date-fns (TypeScript)
DESCRIPTION: This snippet demonstrates a `BlogPostPublishedDate` component that formats a `Date` object (`published`) using the `date-fns` library. It highlights a potential issue where removing the `now` prop (which was used for relative time) and switching to absolute formatting can expose time zone-related hydration mismatches between server and client renders.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/date-formatting-nextjs.mdx#_snippet_5

LANGUAGE: TypeScript
CODE:
```
import {format} from 'date-fns';

type Props = {
  published: Date;
};

export default function BlogPostPublishedDate({published}: Props) {
  // `now` is no longer needed? 🤔
  return <p>{format(published, 'MMM d, yyyy')}</p>;
}
```

----------------------------------------

TITLE: Defining Custom Number Formats in next-intl Messages (JSON)
DESCRIPTION: This JSON snippet shows how to define a custom number format, specifically for currency, within a message string. This allows the `price` placeholder to be formatted as a currency when the message is translated, leveraging ICU syntax.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/numbers.mdx#_snippet_3

LANGUAGE: JSON
CODE:
```
{
  "price": "This product costs {price, number, currency}"
}
```

----------------------------------------

TITLE: Catching Unknown Localized Routes in Next.js App Router
DESCRIPTION: This code defines a catch-all route (`[...rest]/page.tsx`) within a localized segment (`[locale]`) that explicitly calls the `notFound` function. This ensures that any unknown routes within the localized path (e.g., `/en/unknown`) will render the localized `not-found` page.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/error-files.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
import {notFound} from 'next/navigation';

export default function CatchAllPage() {
  notFound();
}
```

----------------------------------------

TITLE: Mapping Enum Values in next-intl Messages (JSON)
DESCRIPTION: This snippet illustrates the use of the `select` argument in `next-intl` messages to map identifiers (like 'female', 'male') to human-readable labels ('She', 'He'). It functions similarly to a `switch` statement, allowing different outputs based on the provided 'gender' value. The `other` case is mandatory and serves as a fallback.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_17

LANGUAGE: json
CODE:
```
"message": "{gender, select, female {She} male {He} other {They}} is online."
```

----------------------------------------

TITLE: useRouter with Dynamic Params (Object Format, next-intl, TSX)
DESCRIPTION: Illustrates how to navigate to a route with dynamic parameters when the `pathnames` setting *is* used. The navigation target is provided as an object containing the internal `pathname` template and a `params` object mapping dynamic segment names to their values. This allows `next-intl` to resolve the correct localized URL.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_10

LANGUAGE: TSX
CODE:
```
// 2. An object (when using `pathnames`)
router.push({
  pathname: '/users/[userId]',
  params: {userId: '5'}
});
```

----------------------------------------

TITLE: Handling GET Requests with next-intl in Next.js Route Handlers (TypeScript)
DESCRIPTION: This snippet demonstrates how to create a Next.js Route Handler (`app/api/hello/route.tsx`) that utilizes `next-intl` for internationalization. It illustrates retrieving the `locale` from a search parameter, validating it with `hasLocale`, and then using `getTranslations` to return a localized JSON response. This handler requires `next-intl`, `next-intl/server`, and `next/server` as dependencies.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/actions-metadata-route-handlers.mdx#_snippet_8

LANGUAGE: tsx
CODE:
```
import {NextResponse} from 'next/server';
import {hasLocale} from 'next-intl';
import {getTranslations} from 'next-intl/server';
import {routing} from '@/i18n/routing';

export async function GET(request) {
  // Example: Receive the `locale` via a search param
  const {searchParams} = new URL(request.url);
  const locale = searchParams.get('locale');
  if (!hasLocale(routing.locales, locale)) {
    return NextResponse.json({error: 'Invalid locale'}, {status: 400});
  }

  const t = await getTranslations({locale, namespace: 'Hello'});
  return NextResponse.json({title: t('title')});
}
```

----------------------------------------

TITLE: Using useTranslations with Type-Safe Message Keys (TypeScript)
DESCRIPTION: This snippet demonstrates the type-safe usage of `useTranslations` with augmented `Messages`. It shows how valid message keys like 'title' are accepted, while unknown keys like 'description' are flagged as errors, ensuring strict message key validation.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/typescript.mdx#_snippet_6

LANGUAGE: tsx
CODE:
```
function About() {
  // ✅ Valid namespace
  const t = useTranslations('About');

  // ✖️ Unknown message key
  t('description');

  // ✅ Valid message key
  t('title');
}
```

----------------------------------------

TITLE: Configuring next-intl Plugin in Next.js (TypeScript)
DESCRIPTION: This configuration sets up the `next-intl` plugin in `next.config.ts`, creating an alias for request-specific i18n configuration, essential for Server Components. It imports `NextConfig` and `createNextIntlPlugin` to extend the Next.js configuration.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/with-i18n-routing.mdx#_snippet_2

LANGUAGE: js
CODE:
```
import {NextConfig} from 'next';
import createNextIntlPlugin from 'next-intl/plugin';

const nextConfig: NextConfig = {};

const withNextIntl = createNextIntlPlugin();
export default withNextIntl(nextConfig);
```

----------------------------------------

TITLE: Accessing Configured Time Zone with `useTimeZone` and `getTimeZone` in Next.js
DESCRIPTION: Explains how to retrieve the currently configured time zone using `useTimeZone` for client components and `getTimeZone` for async server components. These utilities allow components to react to or display the active time zone setting.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_22

LANGUAGE: tsx
CODE:
```
// Regular components
import {useTimeZone} from 'next-intl';
const timeZone = useTimeZone();

// Async Server Components
import {getTimeZone} from 'next-intl/server';
const timeZone = await getTimeZone();
```

----------------------------------------

TITLE: Strictly Typing Locale in next-intl 4.0 (TSX)
DESCRIPTION: This configuration snippet shows how to strictly type the `Locale` within the `next-intl` module's `AppConfig` interface. By leveraging the `routing.locales` array, it ensures that APIs like `useLocale()` or `<Link />` use app-specific locale types, enhancing type safety across the application.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-4-0.mdx#_snippet_1

LANGUAGE: TSX
CODE:
```
// global.ts

import {routing} from '@/i18n/routing';

declare module 'next-intl' {
  interface AppConfig {
    // ...
    Locale: (typeof routing.locales)[number];
  }
}
```

----------------------------------------

TITLE: Composing Link with Custom Props (TSX)
DESCRIPTION: Illustrates how to create a wrapper component (`StyledLink`) that accepts additional props (like `color`) alongside the standard `Link` props by composing types using `ComponentProps<typeof Link>`. This allows forwarding `Link`'s props while adding custom ones.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_6

LANGUAGE: tsx
CODE:
```
import {ComponentProps} from 'react';
import {Link} from '@/i18n/navigation';

type Props = ComponentProps<typeof Link> & {
  color: 'blue' | 'red';
};

export default function StyledLink({color, href, ...rest}: Props) {
  return <Link href={href} style={{color}} {...rest} />;
}
```

----------------------------------------

TITLE: Augmenting Locale Type with i18n Routing (TypeScript)
DESCRIPTION: This snippet shows how to augment the `Locale` type in `AppConfig` using `next-intl`'s i18n routing configuration. It dynamically derives the possible locale values from `routing.locales`, ensuring consistency with your routing setup.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/typescript.mdx#_snippet_3

LANGUAGE: tsx
CODE:
```
import {routing} from '@/i18n/routing';

declare module 'next-intl' {
  interface AppConfig {
    // ...
    Locale: (typeof routing.locales)[number];
  }
}
```

----------------------------------------

TITLE: Setting Document Direction for RTL Languages in Next.js (TSX)
DESCRIPTION: Illustrates how to set the `dir` attribute on the `<html>` element of a Next.js application using `rtl-detect`. This ensures the entire document correctly renders for right-to-left languages, a crucial step for proper RTL localization.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_35

LANGUAGE: tsx
CODE:
```
import {getLangDir} from 'rtl-detect';

export default async function RootLayout(/* ... */) {
  const locale = await getLocale();
  const direction = getLangDir(locale);

  return (
    <html lang={locale} dir={direction}>
      {/* ... */}
    </html>
  );
}
```

----------------------------------------

TITLE: Using Type-Safe Global Formats in a React Component (TSX)
DESCRIPTION: This TSX component demonstrates the application of `useFormatter` with type-safe global formats. It illustrates that using an 'unknown' format string will result in a type error, while valid format names like 'short', 'precise', and 'enumeration' are correctly recognized and applied.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/typescript.mdx#_snippet_14

LANGUAGE: tsx
CODE:
```
function Component() {
  const format = useFormatter();

  // ✖️ Unknown format string
  format.dateTime(new Date(), 'unknown');

  // ✅ Valid format
  format.dateTime(new Date(), 'short');

  // ✅ Valid format
  format.number(2, 'precise');

  // ✅ Valid format
  format.list(['HTML', 'CSS', 'JavaScript'], 'enumeration');
}
```

----------------------------------------

TITLE: Handling Custom Rewrites with next-intl Middleware (TypeScript)
DESCRIPTION: This snippet demonstrates how to perform custom URL rewrites before `next-intl` processes the request. It specifically rewrites requests for `/[locale]/profile` to `/[locale]/profile/new` if a `NEW_PROFILE` cookie is set to `true`. This allows for A/B testing or feature flagging based on user cookies, ensuring the internationalization middleware receives the rewritten path.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/middleware.mdx#_snippet_4

LANGUAGE: TypeScript
CODE:
```
import createMiddleware from 'next-intl/middleware';
import {NextRequest} from 'next/server';

export default async function middleware(request: NextRequest) {
  const [, locale, ...segments] = request.nextUrl.pathname.split('/');

  if (locale != null && segments.join('/') === 'profile') {
    const usesNewProfile =
      (request.cookies.get('NEW_PROFILE')?.value || 'false') === 'true';

    if (usesNewProfile) {
      request.nextUrl.pathname = `/${locale}/profile/new`;
    }
  }

  const handleI18nRouting = createMiddleware({
    locales: ['en', 'de'],
    defaultLocale: 'en'
  });
  const response = handleI18nRouting(request);
  return response;
}

export const config = {
  matcher: ['/', '/(de|en)/:path*']
};
```

----------------------------------------

TITLE: Passing Dynamic Parameters for Sitemap Entries in Next.js
DESCRIPTION: This example demonstrates two ways to pass dynamic parameters when generating sitemap entries, depending on whether the `pathnames` setting is used with `next-intl`. It shows how to provide a final string for simple paths or an object with `pathname` and `params` for more complex, dynamic routes.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/actions-metadata-route-handlers.mdx#_snippet_7

LANGUAGE: TSX
CODE:
```
// 1. A final string (when not using `pathnames`)
getEntries('/users/1');

// 2. An object (when using `pathnames`)
getEntries({
  pathname: '/users/[id]',
  params: {id: '1'}
});
```

----------------------------------------

TITLE: Referencing Global Formats in JSON Messages (Localization)
DESCRIPTION: Illustrates how to reference globally defined `date` and `number` formats directly within JSON message files. This allows for dynamic formatting of values embedded in translated strings, ensuring consistency.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_29

LANGUAGE: json
CODE:
```
{
  "ordered": "You've ordered this product on {orderDate, date, short}",
  "latitude": "Latitude: {latitude, number, precise}"
}
```

----------------------------------------

TITLE: Providing Locale to NextIntlClientProvider (TSX)
DESCRIPTION: This snippet illustrates how to pass the locale to the `NextIntlClientProvider` component. This provider makes the locale available to client components and ensures proper internationalization for rendered output.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_8

LANGUAGE: TSX
CODE:
```
<NextIntlClientProvider locale="en">...</NextIntlClientProvider>
```

----------------------------------------

TITLE: Integrating Client-Side Error Handling Provider in RootLayout (TSX)
DESCRIPTION: This snippet demonstrates how to integrate the `IntlErrorHandlingProvider` (a client component) within the `RootLayout` (a server component). The outer `NextIntlClientProvider` handles general inheritance, while the inner provider adds specific non-serializable error handling functions.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_5

LANGUAGE: tsx
CODE:
```
import {NextIntlClientProvider} from 'next-intl';
import {getLocale} from 'next-intl/server';
import IntlErrorHandlingProvider from './IntlErrorHandlingProvider';

export default async function RootLayout({children}) {
  const locale = await getLocale();

  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider>
          <IntlErrorHandlingProvider>{children}</IntlErrorHandlingProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
```

----------------------------------------

TITLE: Handling Non-Localized Unknown Routes with Root `not-found.tsx`
DESCRIPTION: This snippet shows a root `not-found.tsx` file that catches requests not matched by the `next-intl` middleware, typically non-localized paths. It uses `next/error` to render a generic 404 page, requiring a root layout to be present for it to function correctly.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/error-files.mdx#_snippet_2

LANGUAGE: tsx
CODE:
```
'use client';

import Error from 'next/error';

export default function NotFound() {
  return (
    <html lang="en">
      <body>
        <Error statusCode={404} />
      </body>
    </html>
  );
}
```

----------------------------------------

TITLE: Using next-intl with Enum-based Values (JavaScript)
DESCRIPTION: This JavaScript snippet shows how to call the `t` (translation) function in `next-intl` to retrieve a message that uses enum-based value selection. It passes an object with the `gender` key set to 'female', which will resolve to 'She is online.' based on the message definition. This demonstrates how to provide the dynamic value for the `select` argument.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_18

LANGUAGE: js
CODE:
```
t('message', {gender: 'female'}); // "She is online."
```

----------------------------------------

TITLE: Embedding Numbers in next-intl Messages with ICU Syntax (JSON)
DESCRIPTION: This JSON snippet demonstrates how to embed numbers directly within message strings using ICU syntax. It provides examples for basic number formatting, percentage formatting, and custom formatting with a specified number of fraction digits, indicated by the leading `::` for skeletons.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/numbers.mdx#_snippet_2

LANGUAGE: JSON
CODE:
```
{
  "basic": "Basic formatting: {value, number}",
  "percentage": "Displayed as a percentage: {value, number, percent}",
  "custom": "At most 2 fraction digits: {value, number, ::.##}"
}
```

----------------------------------------

TITLE: Formatting String Lists with next-intl's useFormatter (JavaScript)
DESCRIPTION: Demonstrates how to use the `useFormatter` hook from `next-intl` to format an array of strings into a localized list. It shows examples for both 'conjunction' (e.g., 'and') and 'disjunction' (e.g., 'or') types, adapting separators based on the current locale.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/lists.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import {useFormatter} from 'next-intl';

function Component() {
  const format = useFormatter();
  const items = ['HTML', 'CSS', 'JavaScript'];

  // Renders "HTML, CSS, and JavaScript"
  format.list(items, {type: 'conjunction'});

  // Renders "HTML, CSS, or JavaScript"
  format.list(items, {type: 'disjunction'});
}
```

----------------------------------------

TITLE: Rendering Dynamic Arrays of Messages with useMessages in next-intl (TSX)
DESCRIPTION: Demonstrates how to dynamically iterate over all available message keys within a namespace using the `useMessages` hook. This approach is useful when the number of items in a list varies by locale, allowing for flexible rendering without hardcoding keys.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_33

LANGUAGE: tsx
CODE:
```
import {useTranslations, useMessages} from 'next-intl';

function CompanyStats() {
  const t = useTranslations('CompanyStats');

  const messages = useMessages();
  const keys = Object.keys(messages.CompanyStats);

  return (
    <ul>
      {keys.map((key) => (
        <li key={key}>
          <h2>{t(`${key}.title`)}</h2>
          <p>{t(`${key}.value`)}</p>
        </li>
      ))}
    </ul>
  );
}
```

----------------------------------------

TITLE: Accessing Message with Cardinal Pluralization - JavaScript
DESCRIPTION: This JavaScript snippet demonstrates how to use the `t` function to retrieve a pluralized message. The `count` parameter is passed, and `next-intl` automatically selects the correct plural form based on the number and locale rules.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_15

LANGUAGE: javascript
CODE:
```
t('message', {count: 3580}); // "You have 3,580 followers."
```

----------------------------------------

TITLE: Handling Request Locale with hasLocale in next-intl 4.0 (TypeScript)
DESCRIPTION: This example demonstrates using `getRequestConfig` and the new `hasLocale` function to determine the active locale based on the request. It validates the `requestLocale` against a predefined list of supported locales, falling back to a default if invalid, ensuring a valid locale is always used for message loading.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-4-0.mdx#_snippet_2

LANGUAGE: TypeScript
CODE:
```
import {getRequestConfig} from 'next-intl/server';
import {hasLocale} from 'next-intl';
import {routing} from './routing';

export default getRequestConfig(async ({requestLocale}) => {
  // Typically corresponds to the `[locale]` segment
  const requested = await requestLocale;
  const locale = hasLocale(routing.locales, requested)
    ? requested
    : routing.defaultLocale;

  return {
    locale,
    messages: (await import(`../../messages/${locale}.json`)).default
  };
});
```

----------------------------------------

TITLE: Importing and Using next-intl Locale Type (TypeScript)
DESCRIPTION: This snippet illustrates how to import the `Locale` type from `next-intl` to enforce type safety when passing locale values to custom functions. It ensures that any function parameter expecting a locale adheres to the strictly-typed `Locale` definition configured in the application, improving code reliability.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-4-0.mdx#_snippet_3

LANGUAGE: TypeScript
CODE:
```
import {Locale} from 'next-intl';

async function getPosts(locale: Locale) {
  // ...
}
```

----------------------------------------

TITLE: Accessing Nested Messages in React (TypeScript)
DESCRIPTION: This TypeScript React component demonstrates how to consume messages from a deeply nested structure using `useTranslations`. By providing the base path 'auth.SignUp' to the hook, the `t` function can then resolve further nested keys like 'title' or 'form.placeholder' using dot notation, simplifying access within the component.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_4

LANGUAGE: TypeScript
CODE:
```
import {useTranslations} from 'next-intl';

function SignUp() {
  // Provide the lowest common denominator that contains
  // all messages this component needs to consume.
  const t = useTranslations('auth.SignUp');

  return (
    <>
      <h1>{t('title')}</h1>
      <form>
        <input
          // The remaining hierarchy can be resolved by
          // using `.` to access nested messages.
          placeholder={t('form.placeholder')}
        />
        <button type="submit">{t('form.submit')}</button>
      </>
  );
}
```

----------------------------------------

TITLE: Using `t` function with numeric argument (TSX)
DESCRIPTION: This snippet shows a basic usage of the `t` function with a numeric `count` argument. It highlights a common pitfall where the number might not be formatted correctly without specifying the `number` ICU format, leading to unformatted output.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-4-0.mdx#_snippet_5

LANGUAGE: tsx
CODE:
```
t('followers', {count: 30000});
```

----------------------------------------

TITLE: Merging Messages for Locale Fallbacks (TypeScript)
DESCRIPTION: This example demonstrates how to implement message fallbacks by merging locale-specific messages with default messages using the `deepmerge` library. This approach ensures that any missing keys in a specific locale are filled in from the default locale.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_15

LANGUAGE: tsx
CODE:
```
import deepmerge from 'deepmerge';

const userMessages = (await import(`../../messages/${locale}.json`)).default;
const defaultMessages = (await import(`../../messages/en.json`)).default;
const messages = deepmerge(defaultMessages, userMessages);
```

----------------------------------------

TITLE: Embedding Dates with Standard Format in JSON
DESCRIPTION: This JSON snippet demonstrates how to embed a date within a message using the standard 'medium' format provided by ICU syntax. It specifies that the `orderDate` variable should be formatted as a date with a predefined style.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/dates-times.mdx#_snippet_11

LANGUAGE: JSON
CODE:
```
{
  "ordered": "Ordered on {orderDate, date, medium}"
}
```

----------------------------------------

TITLE: Bypassing i18n Middleware for Open Graph Images in Next.js
DESCRIPTION: This configuration snippet for `middleware.ts` shows how to adapt the Next.js matcher to bypass internationalization routing for Open Graph image requests. This is necessary when `localePrefix` is customized, as Next.js middleware might not recognize the rewritten routes, ensuring the `opengraph-image.tsx` file remains accessible.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/actions-metadata-route-handlers.mdx#_snippet_4

LANGUAGE: TSX
CODE:
```
// ...

export const config = {
  matcher: [
    // Skip all paths that should not be internationalized
    '/((?!api|_next|_vercel|.*/opengraph-image|.*\\..*).*)'

    // ...
  ]
};
```

----------------------------------------

TITLE: Formatting React Element Lists with next-intl's useFormatter (TypeScript/React)
DESCRIPTION: Illustrates how to format an array of React elements into a localized list using `next-intl`'s `useFormatter` hook. It maps user data to anchor tags and then passes these elements to `format.list`, which returns an `Iterable<ReactElement>`.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/lists.mdx#_snippet_1

LANGUAGE: TypeScript
CODE:
```
import {useFormatter} from 'next-intl';

function Component() {
  const format = useFormatter();

  const users = [
    {id: 1, name: 'Alice'},
    {id: 2, name: 'Bob'},
    {id: 3, name: 'Charlie'}
  ];

  const items = users.map((user) => (
    <a key={user.id} href={`/user/${user.id}`}>
      {user.name}
    </a>
  ));

  return <p>{format.list(items)}</p>;
}
```

----------------------------------------

TITLE: Updating Relative Time Continuously with `updateInterval` (JS)
DESCRIPTION: Demonstrates configuring the `useNow` hook with an `updateInterval` option to automatically refresh the `now` value at specified intervals. This enables continuous updates for relative time displays on the client side, such as '2 hours ago' changing over time.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/dates-times.mdx#_snippet_7

LANGUAGE: js
CODE:
```
import {useNow, useFormatter} from 'next-intl';

function Component() {
  // Use the global now value initially …
  const now = useNow({
    // … and update it every 10 seconds
    updateInterval: 1000 * 10
  });

  const format = useFormatter();
  const dateTime = new Date('2020-11-20T10:36:01.516Z');

  // Renders e.g. "2 hours ago" and updates continuously
  format.relativeTime(dateTime, now);
}
```

----------------------------------------

TITLE: Configuring Locale with i18n Routing in next-intl (TypeScript)
DESCRIPTION: This snippet demonstrates how to configure the locale in `i18n/request.ts` when i18n routing is enabled. It reads the `requestLocale` parameter, which typically corresponds to the `[locale]` segment, and falls back to a default locale if the requested locale is invalid.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_6

LANGUAGE: TSX
CODE:
```
export default getRequestConfig(async ({requestLocale}) => {
  // Typically corresponds to the `[locale]` segment
  const requested = await requestLocale;
  const locale = hasLocale(routing.locales, requested)
    ? requested
    : routing.defaultLocale;

  return {
    locale
    // ...
  };
});
```

----------------------------------------

TITLE: Removing Redundant Props from NextIntlClientProvider (Diff)
DESCRIPTION: This diff snippet illustrates how to remove the `messages` and `formats` props from `NextIntlClientProvider`. Due to improved inheritance, these props are now automatically provided, simplifying the configuration and reducing boilerplate.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-4-0.mdx#_snippet_8

LANGUAGE: diff
CODE:
```
<NextIntlClientProvider
-  messages={messages}
-  formats={formats}
>
  ...
</NextIntlClientProvider>
```

----------------------------------------

TITLE: Loading Intl Polyfills with Next.js Script in TypeScript
DESCRIPTION: This component dynamically loads required Intl API polyfills from Cloudflare's polyfill service. It leverages the `useLocale` hook from `next-intl` to fetch locale-specific polyfills and uses `next/script` to inject them into the document before interactive content, ensuring broad browser compatibility for internationalization features.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/runtime-requirements.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import {useLocale} from 'next-intl';
import Script from 'next/script';

function IntlPolyfills() {
  const locale = useLocale();

  const polyfills = [
    'Intl',
    'Intl.Locale',
    'Intl.DateTimeFormat',
    `Intl.DateTimeFormat.~locale.${locale}`,
    `Intl.NumberFormat`,
    `Intl.NumberFormat.~locale.${locale}`,
    'Intl.PluralRules',
    `Intl.PluralRules.~locale.${locale}`,
    'Intl.RelativeTimeFormat',
    `Intl.RelativeTimeFormat.~locale.${locale}`,
    'Intl.ListFormat',
    `Intl.ListFormat.~locale.${locale}`
  ];

  return (
    <Script
      strategy="beforeInteractive"
      src={
        'https://cdnjs.cloudflare.com/polyfill/v3/polyfill.min.js?features=' +
        polyfills.join(',')
      }
    />
  );
}
```

----------------------------------------

TITLE: Redirecting to a Localized Path with next-intl (TypeScript)
DESCRIPTION: Demonstrates the basic usage of the `redirect` function from `next-intl` to redirect to a localized path. It shows how to specify the target path using `href` and the required `locale`, and how to include search parameters using the `query` option.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_13

LANGUAGE: TypeScript
CODE:
```
import {redirect} from '@/i18n/navigation';

// Redirects to `/en/login`
redirect({href: '/login', locale: 'en'});

// Search params can be added via `query`
redirect({href: '/users', query: {sortBy: 'name'}, locale: 'en'});
```

----------------------------------------

TITLE: Defining Global Formats in next-intl (Server-side)
DESCRIPTION: Configures global date, number, and list formats for `next-intl` on the server side using `getRequestConfig`. These formats can then be referenced throughout the application for consistent internationalization.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_26

LANGUAGE: tsx
CODE:
```
import {getRequestConfig} from 'next-intl/server';

export default getRequestConfig(async () => {
  return {
    formats: {
      dateTime: {
        short: {
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        }
      },
      number: {
        precise: {
          maximumFractionDigits: 5
        }
      },
      list: {
        enumeration: {
          style: 'long',
          type: 'conjunction'
        }
      }
    }

    // ...
  };
});
```

----------------------------------------

TITLE: Formatting Relative Times with `useFormatter` in Next.js (JavaScript)
DESCRIPTION: This snippet shows how to use the `relativeTime` function from `next-intl`'s `useFormatter` hook to format time differences between two `Date` objects. It calculates and renders relative time strings (e.g., '2 hours ago'), noting that values are rounded.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/dates-times.mdx#_snippet_3

LANGUAGE: JavaScript
CODE:
```
import {useFormatter} from 'next-intl';

function Component() {
  const format = useFormatter();
  const dateTime = new Date('2020-11-20T08:30:00.000Z');

  // A reference point in time
  const now = new Date('2020-11-20T10:36:00.000Z');

  // This will render "2 hours ago"
  format.relativeTime(dateTime, now);
}
```

----------------------------------------

TITLE: Handling Dynamic Pagination State with next-intl (TypeScript)
DESCRIPTION: This snippet demonstrates how to integrate dynamic pagination state (curPage, totalPages) into translated messages using next-intl's useTranslations hook. It shows a functional component that retrieves a translation function 't' for the 'Pagination' namespace and renders a paragraph with interpolated values for the current page and total pages. This approach is suitable when dynamic state is managed on the server side, for instance, via URL parameters.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/server-client-components.mdx#_snippet_8

LANGUAGE: tsx
CODE:
```
function Pagination({curPage, totalPages}) {
  const t = useTranslations('Pagination');
  return <p>{t('info', {curPage, totalPages})}</p>;
}
```

----------------------------------------

TITLE: Supabase Session Update Utility for next-intl Integration (TypeScript)
DESCRIPTION: This utility function, `updateSession`, is designed to integrate Supabase authentication with `next-intl`'s middleware. It creates a Supabase client, retrieves user data, and updates cookies on the `NextResponse` object. Crucially, it accepts an existing `NextResponse` object, allowing it to be chained with other middleware responses, such as the one generated by `next-intl`, ensuring seamless session management.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/middleware.mdx#_snippet_6

LANGUAGE: TypeScript
CODE:
```
import {createServerClient} from '@supabase/ssr';
import {NextResponse, type NextRequest} from 'next/server';

export async function updateSession(
  request: NextRequest,
  response: NextResponse
) {
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({name, value}) =>
            request.cookies.set(name, value)
          );
          cookiesToSet.forEach(({name, value, options}) =>
            response.cookies.set(name, value, options)
          );
        }
      }
    }
  );

  const {
    data: {user}
  } = await supabase.auth.getUser();

  return response;
}
```

----------------------------------------

TITLE: Reusing Rich Text Tags Across Components (next-intl, React)
DESCRIPTION: This example illustrates a pattern for defining and reusing common rich text tags (like paragraph, bold, and italic) across your application. It uses a shared `RichText` component that provides these tags via a render prop, promoting consistency and reusability.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_24

LANGUAGE: javascript
CODE:
```
import {useTranslations} from 'next-intl';
import RichText from '@/components/RichText';

function AboutPage() {
  const t = useTranslations('AboutPage');
  return <RichText>{(tags) => t.rich('description', tags)}</RichText>;
}
```

LANGUAGE: typescript
CODE:
```
import {ReactNode} from 'react';

// These tags are available
type Tag = 'p' | 'b' | 'i';

type Props = {
  children(tags: Record<Tag, (chunks: ReactNode) => ReactNode>): ReactNode
};

export default function RichText({children}: Props) {
  return (
    <div className="prose">
      {children({
        p: (chunks: ReactNode) => <p>{chunks}</p>,
        b: (chunks: ReactNode) => <b className="font-semibold">{chunks}</b>,
        i: (chunks: ReactNode) => <i className="italic">{chunks}</i>
      })}
    </div>
  );
}
```

----------------------------------------

TITLE: Using the Locale Type in next-intl (TypeScript)
DESCRIPTION: This snippet demonstrates how to use the `Locale` type provided by `next-intl` to strongly type locale parameters in functions, enhancing type safety. By default, `Locale` is `string`, but it can be augmented to a strict union of supported locales.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_11

LANGUAGE: tsx
CODE:
```
import {Locale} from 'next-intl';

async function getPosts(locale: Locale) {
  // ...
}
```

----------------------------------------

TITLE: Loading Messages from Remote Sources with Crowdin OTA (TypeScript)
DESCRIPTION: This snippet shows an example of loading internationalization messages from a remote source using the Crowdin OTA JS Client. It includes logic to fall back to a local default locale file if the current locale matches the default, ensuring message availability.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_14

LANGUAGE: tsx
CODE:
```
import OtaClient from '@crowdin/ota-client';

const defaultLocale = 'en';
const client = new OtaClient('<distribution-hash>');
const messages =
  locale === defaultLocale
    ? (await import(`../../messages/en.json`)).default
    : await client.getStringsByLocale(locale);
```

----------------------------------------

TITLE: Configuring Stricter Domain Routing with next-intl (TSX)
DESCRIPTION: This TypeScript JSX snippet shows an example of configuring `next-intl` routing with stricter `domains` settings. It defines specific locales, a default locale, and domain-specific locale prefixes, ensuring that each locale is tied to a single domain as required by the new constraints.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-4-0.mdx#_snippet_10

LANGUAGE: tsx
CODE:
```
import {defineRouting} from 'next-intl/routing';

export const routing = defineRouting({
  locales: ['sv-SE', 'en-SE', 'no-NO', 'en-NO'],
  defaultLocale: 'en-SE',
  localePrefix: {
    mode: 'as-needed',
    prefixes: {
      'en-SE': '/en',
      'en-NO': '/en'
    }
  },
  domains: [
    {
      domain: 'example.se',
      defaultLocale: 'sv-SE',
      locales: ['sv-SE', 'en-SE']
    },
    {
      domain: 'example.no',
      defaultLocale: 'no-NO',
      locales: ['no-NO', 'en-NO']
    }
  ]
});
```

----------------------------------------

TITLE: Rendering next-intl Components in Vitest
DESCRIPTION: This snippet demonstrates how to render a `next-intl` component (`UserProfile`) within a Vitest unit test. It uses `NextIntlClientProvider` to supply the necessary locale and messages, ensuring the component can access internationalization data during testing. The `render` function from `@testing-library/react` is used to mount the component.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/testing.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import {render} from '@testing-library/react';
import {NextIntlClientProvider} from 'next-intl';
import {expect, it} from 'vitest';
import messages from '../../messages/en.json';
import UserProfile from './UserProfile';

it('renders', () => {
  render(
    <NextIntlClientProvider locale="en" messages={messages}>
      <UserProfile />
    </NextIntlClientProvider>
  );
});
```

----------------------------------------

TITLE: Configuring NextIntlClientProvider for Multiple Languages in Next.js Pages Router (`_app.tsx`)
DESCRIPTION: This snippet configures the `NextIntlClientProvider` in `_app.tsx` for a Next.js Pages Router application supporting multiple languages. It uses `useRouter` to dynamically set the `locale` based on the current route and passes `pageProps.messages` for translations. This setup is crucial for enabling internationalization across the entire application.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/pages-router.mdx#_snippet_0

LANGUAGE: jsx
CODE:
```
import {NextIntlClientProvider} from 'next-intl';
import {useRouter} from 'next/router';

export default function App({Component, pageProps}) {
  const router = useRouter();

  return (
    <NextIntlClientProvider
      locale={router.locale}
      timeZone="Europe/Vienna"
      messages={pageProps.messages}
    >
      <Component {...pageProps} />
    </NextIntlClientProvider>
  );
}
```

----------------------------------------

TITLE: Formatting Date and Time Ranges with `dateTimeRange` (JS)
DESCRIPTION: Illustrates the use of the `dateTimeRange` function from `next-intl`'s `useFormatter` hook to format a range between two `Date` objects. It demonstrates how to pass options to control the output format, such as displaying year, month, and day.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/dates-times.mdx#_snippet_9

LANGUAGE: js
CODE:
```
import {useFormatter} from 'next-intl';

function Component() {
  const format = useFormatter();
  const dateTimeA = new Date('2020-11-20T08:30:00.000Z');
  const dateTimeB = new Date('2021-01-24T08:30:00.000Z');

  // Renders "Nov 20, 2020 – Jan 24, 2021"
  format.dateTimeRange(dateTimeA, dateTimeB, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}
```

----------------------------------------

TITLE: Using useLocale with Augmented Locale Type (TypeScript)
DESCRIPTION: This example shows how the `useLocale` hook from `next-intl` benefits from the augmented `Locale` type, providing strict type checking and autocompletion for the returned locale value, ensuring it matches the defined locales.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/typescript.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
import {useLocale} from 'next-intl';

// ✅ 'en' | 'de'
const locale = useLocale();
```

----------------------------------------

TITLE: Using `now` in Server Components with `dynamicIO` (TSX)
DESCRIPTION: Shows how to use `getNow` and `getFormatter` from `next-intl/server` in an async Server Component. The `'use cache'` directive is added to specify cache expiration, ensuring consistent `now` values when `dynamicIO` is enabled in Next.js.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/dates-times.mdx#_snippet_6

LANGUAGE: tsx
CODE:
```
import {getNow, getFormatter} from 'next-intl/server';

async function FormattedDate({date}) {
  'use cache';

  const now = await getNow();
  const format = await getFormatter();

  return format.relativeTime(date, now);
}
```

----------------------------------------

TITLE: Defining Nested Message Structure in JSON
DESCRIPTION: This JSON example demonstrates structuring internationalization messages using deeply nested objects. This allows for more granular organization of messages, such as grouping all sign-up related messages under 'auth.SignUp' and further subdividing form-related messages under 'form'. This provides a clear hierarchy for complex message sets.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_3

LANGUAGE: JSON
CODE:
```
{
  "auth": {
    "SignUp": {
      "title": "Sign up",
      "form": {
        "placeholder": "Please enter your name",
        "submit": "Submit"
      }
    }
  }
}
```

----------------------------------------

TITLE: Splitting and Merging Messages from Multiple Files (TypeScript)
DESCRIPTION: This snippet illustrates how to organize internationalization messages into multiple files (e.g., by feature or section) and then merge them at runtime into a single messages object. This can improve organization for large projects.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_16

LANGUAGE: tsx
CODE:
```
const messages = {
  ...(await import(`../../messages/${locale}/login.json`)).default,
  ...(await import(`../../messages/${locale}/dashboard.json`)).default
};
```

----------------------------------------

TITLE: Defining Custom DateTimeFormat Options in JavaScript
DESCRIPTION: This JavaScript snippet demonstrates how to define a custom date format using `Intl.DateTimeFormat` options within the `next-intl` `t` function. It specifies the 'short' format, which can be referenced in message definitions, allowing for programmatic control over date rendering.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/dates-times.mdx#_snippet_14

LANGUAGE: JavaScript
CODE:
```
t(
  'ordered',
  {orderDate: new Date('2020-11-20T10:36:01.516Z')},
  {
    dateTime: {
      short: {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
      }
    }
  }
);
```

----------------------------------------

TITLE: Configuring 'always' Locale Prefix in next-intl Routing (TSX)
DESCRIPTION: Sets the `localePrefix` option to `'always'` within the `defineRouting` configuration. This is the default behavior and ensures that all pathnames are prefixed with the locale (e.g., `/en/about`).
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
import {defineRouting} from 'next-intl/routing';

export const routing = defineRouting({
  // ...
  localePrefix: 'always'
});
```

----------------------------------------

TITLE: Defining English Messages - JSON
DESCRIPTION: This JSON file defines the translation messages for the English locale. It provides a structured way to store translatable strings, which `next-intl` uses to render localized content. The example shows a simple 'HomePage' object with a 'title' key.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/without-i18n-routing.mdx#_snippet_1

LANGUAGE: json
CODE:
```
{
  "HomePage": {
    "title": "Hello world!"
  }
}
```

----------------------------------------

TITLE: Defining Structured Messages for Arrays in next-intl (JSON)
DESCRIPTION: Illustrates a structured JSON format for defining internationalized messages that represent a list of items. Each item has nested `title` and `value` keys, providing a clear and organized way to manage data for dynamic lists.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_31

LANGUAGE: json
CODE:
```
{
  "CompanyStats": {
    "yearsOfService": {
      "title": "Years of service",
      "value": "34"
    },
    "happyClients": {
      "title": "Happy clients",
      "value": "1.000+"
    },
    "partners": {
      "title": "Products",
      "value": "5.000+"
    }
  }
}
```

----------------------------------------

TITLE: Creating an Active Navigation Link Component (TSX)
DESCRIPTION: Provides an example of a reusable `NavigationLink` component that uses `useSelectedLayoutSegment` from Next.js to determine if the link's target path matches the current segment, applying styling and `aria-current` accordingly.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
'use client';

import {useSelectedLayoutSegment} from 'next/navigation';
import {ComponentProps} from 'react';
import {Link} from '@/i18n/navigation';

export default function NavigationLink({
  href,
  ...rest
}: ComponentProps<typeof Link>) {
  const selectedLayoutSegment = useSelectedLayoutSegment();
  const pathname = selectedLayoutSegment ? `/${selectedLayoutSegment}` : '/';
  const isActive = pathname === href;

  return (
    <Link
      aria-current={isActive ? 'page' : undefined}
      href={href}
      style={{fontWeight: isActive ? 'bold' : 'normal'}}
      {...rest}
    />
  );
}
```

----------------------------------------

TITLE: Constructing a Localized Pathname with next-intl (TypeScript)
DESCRIPTION: Explains how to use the `getPathname` function to generate a localized pathname string. It shows how to provide the target path via `href` and the desired `locale`, and how to include search parameters using the `query` option. This is useful for tasks like generating canonical links.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_18

LANGUAGE: TypeScript
CODE:
```
import {getPathname} from '@/i18n/navigation';

// Will return `/en/about`
const pathname = getPathname({
  locale: 'en',
  href: '/about'
});

// Search params can be added via `query`
const pathname = getPathname({
  locale: 'en',
  href: {
    pathname: '/users',
    query: {sortBy: 'name'}
  }
});
```

----------------------------------------

TITLE: Opting Out of Prop Inheritance with NextIntlClientProvider (TSX)
DESCRIPTION: This example shows how to explicitly prevent `NextIntlClientProvider` from inheriting certain props, such as `messages`, when rendered from a Server Component. This is useful for selective internationalization control and reducing client bundle size.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_3

LANGUAGE: tsx
CODE:
```
<NextIntlClientProvider
  // Don't pass any messages to the client
  messages={null}
  // ...
>
  ...
</NextIntlClientProvider>
```

----------------------------------------

TITLE: Formatting Date with Time Zone using date-fns-tz (TSX)
DESCRIPTION: This component utilizes the `date-fns-tz` library to format a `Date` object according to a specified `timeZone`. It takes `published` (a Date object) and `timeZone` (a string) as props, returning a paragraph element displaying the formatted date. This ensures dates are displayed consistently regardless of the client's local time zone.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/date-formatting-nextjs.mdx#_snippet_7

LANGUAGE: tsx
CODE:
```
import {format} from 'date-fns-tz';

type Props = {
  published: Date;
  timeZone: string;
};

export default function BlogPostPublishedDate({published, timeZone}: Props) {
  return <p>{format(published, timeZone, 'MMM d, yyyy')}</p>;
}
```

----------------------------------------

TITLE: Defining Component-Scoped Messages in JSON
DESCRIPTION: This JSON snippet demonstrates how to structure internationalization messages using component names as namespaces. The 'About' key acts as a namespace for messages related to the About component, with 'title' being a specific message key within it. This approach promotes modularity and organization of translation strings.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_0

LANGUAGE: JSON
CODE:
```
{
  "About": {
    "title": "About us"
  }
}
```

----------------------------------------

TITLE: Referencing Custom Date Format in JSON
DESCRIPTION: This JSON snippet shows how a message can reference a custom date format named 'short'. This format is then defined programmatically in the application code, allowing for reusable and centralized date formatting configurations.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/dates-times.mdx#_snippet_13

LANGUAGE: JSON
CODE:
```
{
  "ordered": "Ordered on {orderDate, date, short}"
}
```

----------------------------------------

TITLE: Rendering Fixed Arrays of Messages with useTranslations in next-intl (TSX)
DESCRIPTION: Shows how to render a list of internationalized messages by mapping over a predefined array of keys. It uses `useTranslations` to fetch messages and dynamically constructs message keys for `title` and `value`, suitable when the list items are static.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_32

LANGUAGE: tsx
CODE:
```
import {useTranslations} from 'next-intl';

function CompanyStats() {
  const t = useTranslations('CompanyStats');
  const keys = ['yearsOfService', 'happyClients', 'partners'] as const;

  return (
    <ul>
      {keys.map((key) => (
        <li key={key}>
          <h2>{t(`${key}.title`)}</h2>
          <p>{t(`${key}.value`)}</p>
        </li>
      ))}
    </ul>
  );
}
```

----------------------------------------

TITLE: Configuring next-intl localeCookie with Custom Options (TSX)
DESCRIPTION: This snippet demonstrates how to use the `defineRouting` function from `next-intl/routing` to customize the `localeCookie` settings. It shows how to set a custom cookie name (`name`) and configure the expiration time (`maxAge`) in seconds. These custom settings are merged with the default cookie attributes.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_14

LANGUAGE: tsx
CODE:
```
import {defineRouting} from 'next-intl/routing';

export const routing = defineRouting({
  // ...

  // Will be merged with the defaults
  localeCookie: {
    // Custom cookie name
    name: 'USER_LOCALE',
    // Expire in one year
    maxAge: 60 * 60 * 24 * 365
  }
});
```

----------------------------------------

TITLE: Using Formatted Messages with useTranslations (React Component)
DESCRIPTION: Shows how to use the `useTranslations` hook to retrieve and render messages that include references to global formats. It demonstrates passing data (`orderDate`, `latitude`) to the translation function for dynamic formatting.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_30

LANGUAGE: tsx
CODE:
```
import {useTranslations} from 'next-intl';

function Component() {
  const t = useTranslations();

  t('ordered', {orderDate: new Date('2020-11-20T10:36:01.516Z')});
  t('latitude', {latitude: 47.*********});
}
```

----------------------------------------

TITLE: Loading Messages by Base Language (TypeScript)
DESCRIPTION: This example shows how to load messages based on the base language (e.g., 'en') rather than specific regional variants (e.g., 'en-US', 'en-CA'). It uses `Intl.Locale` to extract the language part from the full locale string, simplifying message management for broad language support.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_17

LANGUAGE: tsx
CODE:
```
import {getRequestConfig} from 'next-intl/server';

export default getRequestConfig(async () => {
  // E.g. "en-US", "en-CA", …
  const locale = 'en-US';

  // E.g. "en"
  const language = new Intl.Locale(locale).language;

  // Load messages based on the language
  const messages = (await import(`../../messages/${language}.json`)).default;

  // ...
});
```

----------------------------------------

TITLE: Configuring Jest for next-intl Transformation
DESCRIPTION: This Jest configuration snippet enables `next-intl` to work correctly with Jest, which lacks built-in ESM support. By modifying `transformIgnorePatterns` to exclude `next-intl` from transformation ignores, Jest is instructed to process and transform `next-intl` imports, resolving compatibility issues.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/testing.mdx#_snippet_2

LANGUAGE: tsx
CODE:
```
const nextJest = require('next/jest');

const createJestConfig = nextJest({dir: './'});

module.exports = async () => ({
  ...(await createJestConfig({
    testEnvironment: 'jsdom',
    rootDir: 'src'
  })()),
  // https://github.com/vercel/next.js/issues/40183
  transformIgnorePatterns: ['node_modules/(?!next-intl)/']
});
```

----------------------------------------

TITLE: Reusing Translation Logic with a Custom Hook (TypeScript)
DESCRIPTION: This TypeScript custom hook, `useLocaleLabel`, demonstrates a pattern for reusing translation logic across different components. It initializes `useTranslations` with a specific namespace ('useLocaleLabel') and provides a `getLocaleLabel` function that resolves a message key ('label') with a dynamic `locale` parameter, promoting consistent label generation.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_5

LANGUAGE: TypeScript
CODE:
```
export default function useLocaleLabel() {
  const t = useTranslations('useLocaleLabel');

  function getLocaleLabel(locale: 'en' | 'de') {
    return t('label', {locale});
  }

  return getLocaleLabel;
}
```

----------------------------------------

TITLE: Opting Out of NextIntlClientProvider Prop Inheritance (TSX)
DESCRIPTION: This TypeScript JSX snippet demonstrates how to explicitly opt out of the default inheritance for `messages` and `formats` in `NextIntlClientProvider`. By setting these props to `null`, you prevent them from being passed to the client, reverting to the previous behavior.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-4-0.mdx#_snippet_9

LANGUAGE: tsx
CODE:
```
<NextIntlClientProvider
  // Don't pass these to the client
  messages={null}
  formats={null}
>
  ...
</NextIntlClientProvider>
```

----------------------------------------

TITLE: Integrating next-intl with Auth.js Middleware in Next.js
DESCRIPTION: This snippet demonstrates how to combine `next-intl`'s internationalization middleware with Auth.js (NextAuth.js) middleware in a Next.js application. It handles routing for both public and authenticated pages, ensuring `next-intl` runs correctly based on Auth.js's authorization flow and specific page configurations like `signIn`.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/middleware.mdx#_snippet_8

LANGUAGE: TypeScript
CODE:
```
import {withAuth} from 'next-auth/middleware';
import createMiddleware from 'next-intl/middleware';
import {NextRequest} from 'next/server';
import {routing} from './i18n/routing';

const publicPages = ['/', '/login'];

const handleI18nRouting = createMiddleware(routing);

const authMiddleware = withAuth(
  // Note that this callback is only invoked if
  // the `authorized` callback has returned `true`
  // and not for pages listed in `pages`.
  function onSuccess(req) {
    return handleI18nRouting(req);
  },
  {
    callbacks: {
      authorized: ({token}) => token != null
    },
    pages: {
      signIn: '/login'
    }
  }
);

export default function middleware(req: NextRequest) {
  const publicPathnameRegex = RegExp(
    `^(/(${locales.join('|')}))?(${publicPages
      .flatMap((p) => (p === '/' ? ['', '/'] : p))
      .join('|')})/?$`,
    'i'
  );
  const isPublicPage = publicPathnameRegex.test(req.nextUrl.pathname);

  if (isPublicPage) {
    return handleI18nRouting(req);
  } else {
    return (authMiddleware as any)(req);
  }
}

export const config = {
  matcher: ['/((?!api|_next|.*\..*).*)']
};
```

----------------------------------------

TITLE: Using Cached Date Value in Next.js Page Component (TypeScript)
DESCRIPTION: This snippet shows how to consume the `getNow` utility function (defined using `React.cache`) within a Next.js page component. By calling `getNow()`, the component retrieves a consistent `Date` instance for the current request, ensuring all components rendering within that request use the same timestamp.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/date-formatting-nextjs.mdx#_snippet_4

LANGUAGE: TypeScript
CODE:
```
import getNow from './getNow';

export default function BlogPostPage() {
  // ✅ Will be consistent for the current request,
  // regardless of the timing of different calls
  const now = getNow();
  // ...
}
```

----------------------------------------

TITLE: Disabling Client-Side Message Provision in Next.js with next-intl
DESCRIPTION: This snippet demonstrates how to prevent `next-intl` from passing any messages to the client side by setting the `messages` prop of `NextIntlClientProvider` to `null`. This is useful for optimizing performance when messages are not needed on the client or are loaded dynamically.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/server-client-components.mdx#_snippet_3

LANGUAGE: TypeScript
CODE:
```
<NextIntlClientProvider
  // Don't pass any messages to the client
  messages={null}
>
  ...
</NextIntlClientProvider>
```

----------------------------------------

TITLE: Retrieving Current Pathname (usePathname, next-intl, TSX)
DESCRIPTION: Explains how to use the `usePathname` hook from `next-intl` to retrieve the current pathname. Unlike Next.js's `usePathname`, this hook returns the pathname *without* the locale prefix. When using the `pathnames` setting, it returns the internal pathname template (e.g., `/news/[articleSlug]`), not the resolved URL with dynamic parameter values (e.g., `/news/product-update`). Requires importing `usePathname` from `next-intl/navigation`.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_12

LANGUAGE: TSX
CODE:
```
'use client';

import {usePathname} from '@/i18n/navigation';

// When the user is on `/en`, this will be `/`
const pathname = usePathname();
```

----------------------------------------

TITLE: Conditional Locale Configuration per Domain in next-intl
DESCRIPTION: Illustrates how to use environment variables (like `VERCEL_PROJECT_PRODUCTION_URL`) to conditionally configure routing settings such as `locales`, `defaultLocale`, and `localePrefix` differently for different domains during the build process.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_12

LANGUAGE: tsx
CODE:
```
import {defineRouting} from 'next-intl/routing';

const isUsDomain =
  process.env.VERCEL_PROJECT_PRODUCTION_URL === 'us.example.com';

export const routing = defineRouting({
  locales: isUsDomain ? ['en-US'] : ['en-CA', 'fr-CA'],
  defaultLocale: isUsDomain ? 'en-US' : 'en-CA',
  localePrefix: isUsDomain ? 'never' : 'always'
});
```

----------------------------------------

TITLE: TypeScript Narrowing Workaround for next-intl redirect (Correct)
DESCRIPTION: Provides the recommended workaround for the TypeScript narrowing issue with `redirect`. By returning the call to the `redirect` function, TypeScript can correctly analyze the control flow and narrow the type of variables after the conditional check.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_17

LANGUAGE: TypeScript
CODE:
```
if (!userId) {
  return redirect({href: '/login', locale: 'en'});
}

// ✅ `userId` is narrowed to `string` here
```

----------------------------------------

TITLE: Configuring TypeScript for JSON Type Declarations
DESCRIPTION: This `tsconfig.json` snippet enables `allowArbitraryExtensions`, which is crucial for TypeScript to recognize and process the generated `.d.json.ts` declaration files. This setting bridges the gap in TypeScript's default JSON module inference, allowing for stricter type checking.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/typescript.mdx#_snippet_11

LANGUAGE: json
CODE:
```
{
  "compilerOptions": {
    // ...
    "allowArbitraryExtensions": true
  }
}
```

----------------------------------------

TITLE: Migrating Navigation APIs to Shared Namespace - next-intl v3.0
DESCRIPTION: This snippet demonstrates migrating the `next-intl` navigation APIs (`Link`, `useRouter`, `usePathname`, `redirect`) from individual imports to a shared namespace using `createSharedPathnamesNavigation`. This change introduces type-safety for the `locale` prop and streamlines API usage in Next.js App Router, requiring `locales` as a dependency.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-3-0.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import {createSharedPathnamesNavigation} from 'next-intl/navigation';

const locales = ['en', 'de'] as const;
const {Link, useRouter, usePathname, redirect} = createSharedPathnamesNavigation({locales});
```

----------------------------------------

TITLE: Migrating `getPathname` Function for Locale Prefixing (TypeScript)
DESCRIPTION: This snippet illustrates the change for the `getPathname` function when migrating to `createNavigation`. Manual prepending of the locale prefix is no longer required, as `getPathname` now handles this automatically based on the routing strategy.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-3-22.mdx#_snippet_8

LANGUAGE: diff
CODE:
```
- '/'+ locale + getPathname(/* ... */)
+ getPathname(/* ... */);
```

----------------------------------------

TITLE: Applying Global Formats with NextIntlClientProvider (Client-side)
DESCRIPTION: Applies global date, number, and list formats to client components using the `NextIntlClientProvider`. This ensures consistent formatting for components rendered on the client side, mirroring server-side configurations.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_27

LANGUAGE: tsx
CODE:
```
<NextIntlClientProvider
  formats={{
    dateTime: {
      short: {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
      }
    },
    number: {
      precise: {
        maximumFractionDigits: 5
      }
    },
    list: {
      enumeration: {
        style: 'long',
        type: 'conjunction'
      }
    }
  }}
>
  ...
</NextIntlClientProvider>
```

----------------------------------------

TITLE: Handling Dynamic Redirects with next-intl (TypeScript)
DESCRIPTION: Illustrates how to handle dynamic path segments when using the `redirect` function. It shows two methods: passing a final string (when not using `pathnames`) and passing an object with `pathname` and `params` (when using the `pathnames` setting).
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_14

LANGUAGE: TypeScript
CODE:
```
// 1. A final string (when not using `pathnames`)
redirect({href: '/users/12', locale: 'en'});

// 2. An object (when using `pathnames`)
redirect({
  href: {
    pathname: '/users/[userId]',
    params: {userId: '5'}
  },
  locale: 'en'
});
```

----------------------------------------

TITLE: Lazy Loading next-intl Error Component in Next.js (TSX)
DESCRIPTION: This code demonstrates how to lazy load the actual error component in `error.tsx` to improve initial bundle performance. By exporting a `lazy` reference to a separate `Error` component file, the translation functionality from `next-intl` and the error UI are only loaded when an error actually occurs, reducing the initial load size for performance-sensitive applications.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/error-files.mdx#_snippet_8

LANGUAGE: TSX
CODE:
```
'use client';

import {lazy} from 'react';

// Move error content to a separate chunk and load it only when needed
export default lazy(() => import('./Error'));
```

----------------------------------------

TITLE: Migrating `redirect` Function with Explicit Locale (TypeScript)
DESCRIPTION: This snippet shows the updated syntax for the `redirect` function after migrating to `createNavigation`. It now requires an explicit `locale` argument and the `href` (whether string or object) must be wrapped within an object assigned to the `href` property. This change aligns with Next.js 15 preparations.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-3-22.mdx#_snippet_7

LANGUAGE: diff
CODE:
```
- redirect('/about')
+ redirect({href: '/about', locale})

- redirect({pathname: '/users/[id]', params: {id: 2}})
+ redirect({href: {pathname: '/users/[id]', params: {id: 2}}, locale})
```

----------------------------------------

TITLE: Using Global Formats with `dateTimeRange` (JS)
DESCRIPTION: Shows how to apply a globally configured format to `dateTimeRange` by passing its name as the third argument. It also demonstrates the flexibility to optionally override specific formatting options even when using a global format, allowing for fine-tuned control.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/dates-times.mdx#_snippet_10

LANGUAGE: js
CODE:
```
// Use a global format
format.dateTimeRange(dateTimeA, dateTimeB, 'short');

// Optionally override some options
format.dateTimeRange(dateTimeA, dateTimeB, 'short', {year: 'numeric'});
```

----------------------------------------

TITLE: Setting 'Now' Value via `NextIntlClientProvider` in Next.js
DESCRIPTION: Demonstrates how to pass a specific 'now' value to the `NextIntlClientProvider` component. This ensures that client components wrapped by the provider use this fixed reference point for relative date and time formatting.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_24

LANGUAGE: tsx
CODE:
```
const now = new Date('2024-11-14T10:36:01.516Z');

<NextIntlClientProvider now={now}>...</NextIntlClientProvider>;
```

----------------------------------------

TITLE: Retrieving Server Time Zone in Next.js Server Component (TSX)
DESCRIPTION: This snippet demonstrates how to obtain the server's current time zone using `Intl.DateTimeFormat().resolvedOptions().timeZone` within a Next.js Server Component. The retrieved `timeZone` is then passed as a prop to a child component, ensuring consistent time zone information for date formatting.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/date-formatting-nextjs.mdx#_snippet_6

LANGUAGE: tsx
CODE:
```
export default function BlogPostPage() {
  // ...

  // Use the time zone of the server
  const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  return <BlogPostPublishedDate timeZone={timeZone} published={published} />;
}
```

----------------------------------------

TITLE: Extract Region from next-intl Locale using Intl.Locale (TSX)
DESCRIPTION: Demonstrates how to retrieve the current locale using the `useLocale` hook and then parse it with `Intl.Locale` to extract properties like the region code. Useful when custom prefixes are used but you need details from the underlying locale.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_5

LANGUAGE: tsx
CODE:
```
import {useLocale} from 'next-intl';

function Component() {
  // Assuming the locale is 'en-US'
  const locale = useLocale();

  // Extracts the "US" region
  const {region} = new Intl.Locale(locale);
}
```

----------------------------------------

TITLE: Providing Individual Messages to Client Components with NextIntlClientProvider (TypeScript)
DESCRIPTION: This example illustrates how to selectively provide specific message namespaces to client-side components using NextIntlClientProvider. The Counter component fetches all available messages via useMessages() and then uses lodash/pick to pass only the 'ClientCounter' namespace to its child ClientCounter component, ensuring that only necessary translations are bundled for the client. This is useful for components that cannot be server-rendered but require dynamic message access.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/server-client-components.mdx#_snippet_9

LANGUAGE: tsx
CODE:
```
import pick from 'lodash/pick';
import {NextIntlClientProvider, useMessages} from 'next-intl';
import ClientCounter from './ClientCounter';

export default function Counter() {
  // Receive messages provided in `i18n/request.ts` …
  const messages = useMessages();

  return (
    <NextIntlClientProvider
      messages={
        // … and provide the relevant messages
        pick(messages, 'ClientCounter')
      }
    >
      <ClientCounter />
    </NextIntlClientProvider>
  );
}
```

----------------------------------------

TITLE: Configuring Static Locale without i18n Routing in next-intl (TypeScript)
DESCRIPTION: This snippet shows how to set a static locale in `i18n/request.ts` when i18n routing is not used. The locale can be hardcoded, fetched from user settings, cookies, or headers, providing flexibility for locale determination.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_7

LANGUAGE: TSX
CODE:
```
export default getRequestConfig(async () => {
  // Provide a static locale, fetch a user setting,
  // read from `cookies()`, `headers()`, etc.
  const locale = 'en';

  return {
    locale
    // ...
  };
});
```

----------------------------------------

TITLE: Retrieving Current Date and Time with `useNow` (TSX)
DESCRIPTION: Demonstrates the basic usage of `useNow` and `useFormatter` from `next-intl` to obtain a consistent current date/time and format a relative time. `useNow` ensures consistency across re-renders and supports optional continuous updates.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/dates-times.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
import {useNow, useFormatter} from 'next-intl';

function FormattedDate({date}) {
  const now = useNow();
  const format = useFormatter();

  format.relativeTime(date, now);
}
```

----------------------------------------

TITLE: Disabling Alternate Links in next-intl Routing (TSX)
DESCRIPTION: Configures `next-intl` routing by setting `alternateLinks` to `false` within the `defineRouting` function. This opts out of the middleware's automatic `hreflang` link header generation, allowing manual management of alternate links, for example, in a sitemap.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_16

LANGUAGE: tsx
CODE:
```
import {defineRouting} from 'next-intl/routing';

export const routing = defineRouting({
  // ...

  alternateLinks: false
});
```

----------------------------------------

TITLE: Using createTranslator and createFormatter in Non-React JavaScript
DESCRIPTION: This example illustrates how to use the low-level `createTranslator` and `createFormatter` functions from `use-intl/core` in any JavaScript environment, independent of React. It demonstrates creating a translator instance to handle basic and rich text messages, and a formatter instance for date and time formatting, by directly passing configuration without relying on a provider.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/core-library.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
import {createTranslator, createFormatter} from 'use-intl/core';

const messages = {
  basic: 'Hello {name}!',
  rich: 'Hello <b>{name}</b>!'
};

// This creates the same function that is returned by `useTranslations`.
// Since there's no provider, you can pass all the properties you'd
// usually pass to the provider directly here.
const t = createTranslator({locale: 'en', messages});

// Result: "Hello world!"
t('basic', {name: 'world'});

// To generate HTML markup, you can consider using the `markup`
// function which in contrast to `t.rich` returns a markup string.
t.markup('rich', {
  name: 'world',
  b: (chunks) => `<b>${chunks}</b>`
});

// Creates the same object that is returned by `useFormatter`.
const format = createFormatter({locale: 'en'});

// Result: "Oct 17, 2022"
format.dateTime(new Date(2022, 9, 17), {dateStyle: 'medium'});
```

----------------------------------------

TITLE: TypeScript Narrowing Issue with next-intl redirect (Incorrect)
DESCRIPTION: Highlights a known limitation in TypeScript's control flow analysis where types are not correctly narrowed after calling the `redirect` function without returning it. Shows an example where a variable (`userId`) is not correctly inferred as non-nullable after a conditional check.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_16

LANGUAGE: TypeScript
CODE:
```
import {redirect} from '@/i18n/navigation';

function UserProfile({userId}: {userId?: string}) {
  if (!userId) {
    redirect({href: '/login', locale: 'en'});
  }

  // `userId` should be narrowed to `string` here,
  // but TypeScript doesn't analyze this correctly
}
```

----------------------------------------

TITLE: Configuring Custom `i18n/request.ts` Location in `next.config.mjs` (TypeScript)
DESCRIPTION: This snippet demonstrates how to configure `next-intl` within `next.config.mjs` to specify a custom path for the request configuration file. Using `createNextIntlPlugin`, developers can override the default `i18n/request.ts` location, providing flexibility in file organization.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-3-22.mdx#_snippet_2

LANGUAGE: TypeScript
CODE:
```
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./somewhere/else/request.ts');

// ...
```

----------------------------------------

TITLE: Combining Shared and Component-Specific Rich Text Tags (next-intl, JavaScript)
DESCRIPTION: This snippet demonstrates how to combine globally defined rich text tags with additional, component-specific values (e.g., a username) when using `t.rich`. The spread operator is used to merge these different sets of tags and values effectively.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_25

LANGUAGE: javascript
CODE:
```
function UserPage({username}) {
  const t = useTranslations('UserPage');
  return (
    <RichText>{(tags) => t.rich('description', {...tags, username})}</RichText>
  );
}
```

----------------------------------------

TITLE: Validating Link Component Locale Prop (TypeScript)
DESCRIPTION: This snippet illustrates how the augmented `Locale` type validates the `locale` prop of the `Link` component from `next-intl`'s routing, ensuring that only predefined locale values are accepted, thus improving type safety.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/typescript.mdx#_snippet_2

LANGUAGE: tsx
CODE:
```
import {Link} from '@/i18n/routing';

// ✅ Passes the validation
<Link href="/" locale="en" />;
```

----------------------------------------

TITLE: Configuring Vitest for next-intl ESM Support
DESCRIPTION: This Vitest configuration snippet addresses an issue with `next-intl`'s ESM-only bundling and Next.js's import behavior. By setting `inline: ['next-intl']` under `server.deps`, Vitest is instructed to process imports within `next-intl`, resolving potential module resolution problems during testing.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/testing.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
import {defineConfig} from 'vitest/config';

export default defineConfig({
  test: {
    server: {
      deps: {
        // https://github.com/vercel/next.js/issues/77200
        inline: ['next-intl']
      }
    }
  }
});
```

----------------------------------------

TITLE: Accessing Messages Without Namespace in React (JavaScript)
DESCRIPTION: This JavaScript snippet illustrates how to retrieve all available messages without specifying a namespace when initializing the `useTranslations` hook. When no namespace is provided, the `t` function can then access messages using their full, dot-separated path, such as 'About.title'.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_2

LANGUAGE: JavaScript
CODE:
```
const t = useTranslations();

t('About.title');
```

----------------------------------------

TITLE: Passing Attributes to Rich Text Tags (next-intl, JavaScript)
DESCRIPTION: This section explains that attributes for rich text tags must be set at the call site, not within the translation messages. It also provides a pattern for retrieving attribute values from separate messages and applying them to tags.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_27

LANGUAGE: json
CODE:
```
{
  "message": "Go to <profile>my profile</profile>"
}
```

LANGUAGE: javascript
CODE:
```
t.rich('message', {
  profile: (chunks) => <Link href="/profile">{chunks}</Link>
});
```

LANGUAGE: json
CODE:
```
{
  "message": "See this <partner>partner website</partner>.",
  "partnerHref": "https://partner.example.com"
}
```

LANGUAGE: javascript
CODE:
```
t.rich('message', {
  partner: (chunks) => <a href={t('partnerHref')}>{chunks}</a>
});
```

----------------------------------------

TITLE: Rendering Raw Unparsed Messages with t.raw (next-intl, JavaScript)
DESCRIPTION: This example illustrates how to use `t.raw` to retrieve message content without any parsing, which is useful for injecting raw HTML directly into the DOM using `dangerouslySetInnerHTML`. It's crucial to sanitize the content to prevent XSS attacks.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_29

LANGUAGE: json
CODE:
```
{
  "content": "<h1>Headline</h1><p>This is raw HTML</p>"
}
```

LANGUAGE: javascript
CODE:
```
<div dangerouslySetInnerHTML={{__html: t.raw('content')}} />
```

----------------------------------------

TITLE: Providing Page-Level Messages for Multiple Languages in Next.js Pages Router (`pages/index.tsx`)
DESCRIPTION: This `getStaticProps` function demonstrates how to provide internationalized messages on a page level for a Next.js Pages Router application. It dynamically imports messages from a JSON file based on the `context.locale`, making them available as `pageProps.messages` to the `NextIntlClientProvider`. This ensures that the correct language messages are loaded for each page.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/pages-router.mdx#_snippet_1

LANGUAGE: js
CODE:
```
export async function getStaticProps(context) {
  return {
    props: {
      // You can get the messages from anywhere you like. The recommended
      // pattern is to put them in JSON files separated by locale and read
      // the desired one based on the `locale` received from Next.js.
      messages: (await import(`../../messages/${context.locale}.json`)).default
    }
  };
}
```

----------------------------------------

TITLE: Configuring Middleware Matcher with basePath (TSX)
DESCRIPTION: Defines the `config` object for the `next-intl` middleware, specifying the `matcher` array. It highlights the importance of including the root path (`'/'`) in the matcher when a `basePath` is configured in `next.config.ts`, as the matcher is relative to the `basePath`.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_18

LANGUAGE: tsx
CODE:
```
export const config = {
  // The \`matcher\` is relative to the \`basePath\`
  matcher: [
    // This entry handles the root of the base
    // path and should always be included
    '/'

    // ... other matcher config
  ]
};
```

----------------------------------------

TITLE: Example JSON Message File Structure
DESCRIPTION: This JSON snippet provides an example structure for a message file, defining an 'About' namespace with a 'title' key. This structure is used by `next-intl` for strict message key typing.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/typescript.mdx#_snippet_5

LANGUAGE: json
CODE:
```
{
  "About": {
    "title": "Hello"
  }
}
```

----------------------------------------

TITLE: Accessing Locale in Pages Router for NextIntlClientProvider (TypeScript)
DESCRIPTION: This snippet shows how to access the locale from the Next.js Pages Router using `useRouter` and then pass it to `NextIntlClientProvider`. This is essential for integrating `next-intl` with internationalized routing in the Pages Router.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_10

LANGUAGE: TSX
CODE:
```
import {useRouter} from 'next/router';

// ...

const router = useRouter();

return (
  <NextIntlClientProvider locale={router.locale}>
    ...
  </NextIntlClientProvider>
);
```

----------------------------------------

TITLE: Configuring Next.js Middleware with next-intl (Custom Matcher)
DESCRIPTION: This example extends the middleware configuration to explicitly match pathnames that contain dots, such as `/users/jane.doe`. It shows how to add specific entries to the `matcher` array in `src/middleware.ts` to include paths that would otherwise be excluded by the default pattern.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/with-i18n-routing.mdx#_snippet_7

LANGUAGE: tsx
CODE:
```
// ...

export const config = {
  matcher: [
    // Match all pathnames except for
    // - … if they start with `/api`, `/trpc`, `/_next` or `/_vercel`
    // - … the ones containing a dot (e.g. `favicon.ico`)
    '/((?!api|trpc|_next|_vercel|.*\..*).*)',

    // Match all pathnames within `{/:locale}/users`
    '/([\w-]+)?/users/(.+)'
  ]
};
```

----------------------------------------

TITLE: Customize next-intl Locale Prefixes with Mapping (TSX)
DESCRIPTION: Defines custom user-facing URL prefixes for specific locales using the `prefixes` option within `localePrefix`. The internal locale remains the standard one, and the custom prefix is rewritten internally. Requires adapting the middleware matcher to match the custom prefixes.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
import {defineRouting} from 'next-intl/routing';

export const routing = defineRouting({
  locales: ['en-US', 'de-AT', 'zh'],
  defaultLocale: 'en-US',
  localePrefix: {
    mode: 'always',
    prefixes: {
      'en-US': '/us',
      'de-AT': '/eu/at'
      // (/zh will be used as-is)
    }
  }
});
```

----------------------------------------

TITLE: Running i18n-check for Message Validation (Bash)
DESCRIPTION: This command-line interface (CLI) command executes `i18n-check` to validate message files. It specifies 'en' as the source locale and 'messages' as the directory containing the locale files, checking for issues like missing translations.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/messages.mdx#_snippet_0

LANGUAGE: bash
CODE:
```
i18n-check --source en --locales messages
```

----------------------------------------

TITLE: Customizing Date Format with Skeletons in JSON
DESCRIPTION: This JSON snippet illustrates how to apply a custom date format using an ICU date skeleton. The `::yyyyMMMd` skeleton specifies a format like 'Jul 9, 2024', providing fine-grained control over the date representation within the message.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/dates-times.mdx#_snippet_12

LANGUAGE: JSON
CODE:
```
{
  // Renders e.g. "Ordered on Jul 9, 2024"
  "ordered": "Ordered on {orderDate, date, ::yyyyMMMd}"
}
```

----------------------------------------

TITLE: Parsing and Manipulating Dates with `date-fns` in Next.js (TypeScript)
DESCRIPTION: Since `next-intl` focuses solely on formatting, this snippet demonstrates how to parse dates using the native `Date` constructor and manipulate them with an external library like `date-fns`. It provides an example of subtracting days from a date, emphasizing the need for ISO 8601 conforming date strings.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/dates-times.mdx#_snippet_2

LANGUAGE: TypeScript
CODE:
```
import {subDays} from 'date-fns';

// Make sure your date string conforms to ISO 8601
const date = new Date('2020-11-20T10:36:01.516Z');

// 2020-11-18T10:36:01.516Z
const twoDaysAgo = subDays(date, 2);
```

----------------------------------------

TITLE: Checking Message Availability with t.has in next-intl (JavaScript)
DESCRIPTION: Demonstrates how to use the `t.has` function from `next-intl` to programmatically check if a specific message key exists for the current locale. This is useful for rendering content conditionally based on message availability, preventing errors when messages are optional.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_30

LANGUAGE: js
CODE:
```
const t = useTranslations('About');

t.has('title'); // true
t.has('unknown'); // false
```

----------------------------------------

TITLE: next-intl Middleware Function Signature
DESCRIPTION: This snippet shows the TypeScript type signature for the middleware function returned by `createMiddleware`. It indicates that the middleware accepts a `NextRequest` object and returns a `NextResponse` object, allowing for request and response manipulation within the middleware chain.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/middleware.mdx#_snippet_2

LANGUAGE: TypeScript
CODE:
```
function middleware(request: NextRequest): NextResponse;
```

----------------------------------------

TITLE: Recommended Message File Structure (Filesystem)
DESCRIPTION: This snippet illustrates the recommended file system structure for storing internationalization messages. Colocating messages with application code facilitates quick changes and enables type checking based on the message shape.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_12

LANGUAGE: text
CODE:
```
├── messages
│   ├── en.json
│   ├── de-AT.json
│   └── ...
...
```

----------------------------------------

TITLE: Customizing Link Header in next-intl Middleware (TSX)
DESCRIPTION: Demonstrates how to compose the `next-intl` middleware with custom logic. It uses `http-link-header` to parse the `link` header set by the middleware, filters out the `x-default` entry, and updates the response headers before returning the response. Requires `http-link-header` dependency.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_17

LANGUAGE: tsx
CODE:
```
import createMiddleware from 'next-intl/middleware';
import LinkHeader from 'http-link-header';
import {NextRequest} from 'next/server';
import {routing} from './i18n/routing';

const handleI18nRouting = createMiddleware(routing);

export default async function middleware(request: NextRequest) {
  const response = handleI18nRouting(request);

  // Example: Remove the \`x-default\` entry
  const link = LinkHeader.parse(response.headers.get('link'));
  link.refs = link.refs.filter((entry) => entry.hreflang !== 'x-default');
  response.headers.set('link', link.toString());

  return response;
}
```

----------------------------------------

TITLE: Customizing `relativeTime` Unit (JS)
DESCRIPTION: Explains how to specify a custom unit for `relativeTime` formatting using the second argument. This allows overriding the default unit selection (e.g., 'seconds', 'days') to force a specific unit like 'day', resulting in a desired granularity such as '247 days ago'.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/dates-times.mdx#_snippet_8

LANGUAGE: js
CODE:
```
import {useFormatter} from 'next-intl';

function Component() {
  const format = useFormatter();
  const dateTime = new Date('2020-03-20T08:30:00.000Z');
  const now = new Date('2020-11-22T10:36:00.000Z');

  // Renders "247 days ago"
  format.relativeTime(dateTime, {now, unit: 'day'});
}
```

----------------------------------------

TITLE: Defining Reusable Locale-Specific Messages in JSON
DESCRIPTION: This JSON snippet defines a message structure for the `useLocaleLabel` hook, demonstrating how to handle locale-specific variations within a single message key using ICU MessageFormat. The 'label' key within the 'useLocaleLabel' namespace uses a 'select' formatter to return different strings based on the provided 'locale' parameter, enabling dynamic translation.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_6

LANGUAGE: JSON
CODE:
```
{
  "useLocaleLabel": {
    "label": "{locale, select, en {English} de {German} other {Unknown}}"
  }
}
```

----------------------------------------

TITLE: Using the Active Navigation Link Component (TSX)
DESCRIPTION: Shows how to use the custom `NavigationLink` component within a navigation structure, passing the target `href` and using a translation function `t` for link text.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_5

LANGUAGE: tsx
CODE:
```
<nav>
  <NavigationLink href="/">{t('home')}</NavigationLink>
  <NavigationLink href="/about">{t('about')}</NavigationLink>
  <NavigationLink href="/blog">{t('blog')}</NavigationLink>
</nav>
```

----------------------------------------

TITLE: Configuring i18n Ally in VSCode Settings (JSON)
DESCRIPTION: This snippet configures the i18n Ally VSCode extension within the workspace settings. It specifies the paths where locale messages are stored and sets the key style to 'nested' for better organization. This setup is crucial for the extension to correctly locate and manage translation files.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/vscode-integration.mdx#_snippet_0

LANGUAGE: JSON
CODE:
```
"i18n-ally.localesPaths": ["./path/to/your/messages"], // E.g. "./messages"
"i18n-ally.keystyle": "nested"
```

----------------------------------------

TITLE: Configuring Locale Cookie Expiration for GDPR Compliance (TSX)
DESCRIPTION: This snippet demonstrates how to configure the `localeCookie` `maxAge` attribute within `next-intl`'s `defineRouting` function. It allows developers to increase the locale cookie's expiration time, overriding the default session cookie behavior for GDPR compliance or specific application requirements.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-4-0.mdx#_snippet_7

LANGUAGE: tsx
CODE:
```
// i18n/routing.tsx

import {defineRouting} from 'next-intl/routing';

export const routing = defineRouting({
  // ...

  localeCookie: {
    // Expire in one year
    maxAge: 60 * 60 * 24 * 365
  }
});
```

----------------------------------------

TITLE: Setting Time Zone via `NextIntlClientProvider` in Next.js
DESCRIPTION: Demonstrates how to explicitly pass a time zone value to the `NextIntlClientProvider` component. This allows client components wrapped by this provider to inherit the specified time zone for consistent date and time rendering.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_21

LANGUAGE: tsx
CODE:
```
// The time zone can either be statically defined, read from the
// user profile if you store such a setting, or based on dynamic
// request information like the locale or a cookie.
const timeZone = 'Europe/Vienna';

<NextIntlClientProvider timeZone={timeZone}>...<NextIntlClientProvider>
```

----------------------------------------

TITLE: Handling Locale-based Selection with Underscores (JSON)
DESCRIPTION: This snippet demonstrates using the `select` argument for locale-based mapping in `next-intl` messages. It shows how to map `en_GB` and `en_US` to their respective full names. It highlights the requirement for select values to be alphanumeric with underscores, advising against dashes by showing an example of mapping `en-GB` to `en_GB`.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_19

LANGUAGE: json
CODE:
```
"label": "{locale, select, en_GB {British English} en_US {American English} other {Unknown}}"
```

----------------------------------------

TITLE: Creating Navigation APIs without Defined Locales (TSX)
DESCRIPTION: This snippet shows how to use `createNavigation` when the available locales are not known at build time and might be added or removed at runtime. It imports `createNavigation` and calls it with a configuration object that omits the `locales` property, allowing any string encountered at runtime to be treated as a valid locale. The internationalized navigation APIs are then destructured and exported.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_1

LANGUAGE: tsx
CODE:
```
import {createNavigation} from 'next-intl/navigation';

export const {Link, redirect, usePathname, useRouter, getPathname} =
  createNavigation({
    // ... potentially other routing
    // config, but no `locales` ...
  });
```

----------------------------------------

TITLE: Generating Localized Manifest File with next-intl in Next.js
DESCRIPTION: This code demonstrates how to create a `manifest.ts` file in Next.js using `next-intl` for internationalization. Since the manifest is placed outside the `[locale]` segment, a specific locale (e.g., 'en') must be explicitly provided to `getTranslations` to fetch localized content for fields like the app name.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/actions-metadata-route-handlers.mdx#_snippet_5

LANGUAGE: TSX
CODE:
```
import {MetadataRoute} from 'next';
import {getTranslations} from 'next-intl/server';

export default async function manifest(): Promise<MetadataRoute.Manifest> {
  // Pick a locale that is representative of the app
  const locale = 'en';

  const t = await getTranslations({
    namespace: 'Manifest',
    locale
  });

  return {
    name: t('name'),
    start_url: '/',
    theme_color: '#101E33'
  };
}
```

----------------------------------------

TITLE: Using Self-Closing Tags in Rich Text Messages (next-intl, JavaScript)
DESCRIPTION: This example shows how to handle 'self-closing' tags like `<br>` within `next-intl` rich text messages. Although the React component might be self-closing, the ICU parser requires a closing tag in the message syntax.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_26

LANGUAGE: json
CODE:
```
{
  "message": "Hello,<br></br>how are you?"
}
```

LANGUAGE: javascript
CODE:
```
t.rich('message', {
  br: () => <br />
});
```

----------------------------------------

TITLE: Configuring Domain-Based Routing with next-intl
DESCRIPTION: Demonstrates how to configure domain-based routing using `defineRouting` in next-intl. It shows mapping different domains (`us.example.com`, `ca.example.com`, `fr.example.com`) to specific locales and using a custom `localePrefix` for a specific locale (`fr-CA`).
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_9

LANGUAGE: tsx
CODE:
```
import {defineRouting} from 'next-intl/routing';

export const routing = defineRouting({
  locales: ['en-US', 'en-CA', 'fr-CA', 'fr-FR'],
  defaultLocale: 'en-US',
  domains: [
    {
      domain: 'us.example.com',
      defaultLocale: 'en-US',
      locales: ['en-US']
    },
    {
      domain: 'ca.example.com',
      defaultLocale: 'en-CA',
      locales: ['en-CA', 'fr-CA']
    },
    {
      domain: 'fr.example.com',
      defaultLocale: 'fr-FR',
      locales: ['fr-FR']
    }
  ],
  localePrefix: {
    mode: 'as-needed',
    prefixes: {
      // Cleaner prefix for `ca.example.com/fr`
      'fr-CA': '/fr'
    }
  }
});
```

----------------------------------------

TITLE: Configuring Sherlock with next-intl Plugin (JSON)
DESCRIPTION: This JSON snippet configures the Sherlock VS Code extension within the `project.inlang/settings.json` file. It defines the source and supported language tags, and includes the `@inlang/plugin-next-intl` module. The `pathPattern` specifies how `next-intl` message files are organized, enabling Sherlock to integrate seamlessly with `next-intl` projects.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/vscode-integration.mdx#_snippet_1

LANGUAGE: JSON
CODE:
```
{
  "$schema": "https://inlang.com/schema/project-settings",
  "sourceLanguageTag": "en",
  "languageTags": ["en", "de"],
  "modules": [
    "https://cdn.jsdelivr.net/npm/@inlang/plugin-next-intl@latest/dist/index.js"
  ],
  "plugin.inlang.nextIntl": {
    "pathPattern": "./messages/{languageTag}.json"
  }
}
```

----------------------------------------

TITLE: useRouter with Dynamic Params (String Format, next-intl, TSX)
DESCRIPTION: Shows how to navigate to a route with dynamic parameters when the `pathnames` setting is *not* used. The dynamic parameter value is included directly in the final pathname string passed to `router.push`. This approach is suitable when not using explicit pathname templates.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_9

LANGUAGE: TSX
CODE:
```
// 1. A final string (when not using `pathnames`)
router.push('/users/12');
```

----------------------------------------

TITLE: Escaping Curly Braces in next-intl Messages (JSON)
DESCRIPTION: This snippet demonstrates how to escape curly braces in `next-intl` messages using a single quote (`'`) marker. This allows literal curly braces to be displayed in the final message without being interpreted as interpolation markers. For example, '{name}' will render as '{name}' instead of attempting to interpolate a variable.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_21

LANGUAGE: json
CODE:
```
"message": "Escape curly braces with single quotes (e.g. '{name}')"
```

----------------------------------------

TITLE: Configuring NextIntlClientProvider for Single Language in Next.js Pages Router (`_app.tsx`)
DESCRIPTION: This snippet configures the `NextIntlClientProvider` in `_app.tsx` for a Next.js Pages Router application supporting only a single language. The `locale` is hardcoded to 'en', and `pageProps.messages` are passed for translations. This setup simplifies i18n when only one language is needed, still leveraging `next-intl` for message management.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/pages-router.mdx#_snippet_2

LANGUAGE: jsx
CODE:
```
import {NextIntlClientProvider} from 'next-intl';

export default function App({Component, pageProps}) {
  return (
    <NextIntlClientProvider
      locale="en"
      timeZone="Europe/Vienna"
      messages={pageProps.messages}
    >
      <Component {...pageProps} />
    </NextIntlClientProvider>
  );
}
```

----------------------------------------

TITLE: Configuring Global 'Now' Value in `i18n/request.ts` for Next.js
DESCRIPTION: Shows how to set a global reference point in time ('now') within `i18n/request.ts` for `next-intl`. This value is used for formatting relative dates and times, ensuring consistency, especially useful for testing or caching.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_23

LANGUAGE: tsx
CODE:
```
import {getRequestConfig} from 'next-intl/server';

export default getRequestConfig(async () => {
  return {
    now: new Date('2024-11-14T10:36:01.516Z')

    // ...
  };
});
```

----------------------------------------

TITLE: Configuring next-intl Plugin with Custom Path - TypeScript
DESCRIPTION: This snippet demonstrates how to specify a custom path for the `i18n/request.ts` file when initializing the `next-intl` plugin. This is useful if the default location `./i18n/request.ts` is not preferred, allowing for flexible file organization.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/without-i18n-routing.mdx#_snippet_5

LANGUAGE: js
CODE:
```
const withNextIntl = createNextIntlPlugin(
  // Specify a custom path here
  './somewhere/else/request.ts'
);
```

----------------------------------------

TITLE: Implementing CMS-Driven Catch-All Page with next-intl
DESCRIPTION: Provides an example of a Next.js page component (`page.tsx`) that acts as a catch-all route to fetch content from an external CMS based on the locale and dynamic slug segments. It handles fetching content and showing a 404 if not found.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_8

LANGUAGE: tsx
CODE:
```
import {notFound} from 'next';
import {Locale} from 'next-intl';
import {fetchContent} from './cms';

type Props = {
  params: Promise<{
    locale: Locale;
    slug: Array<string>;
  }>;
};

export default async function CatchAllPage({params}: Props) {
  const {locale, slug} = await params;
  const content = await fetchContent(locale, slug);
  if (!content) notFound();

  // ...
}
```

----------------------------------------

TITLE: Providing Page-Level Messages for Single Language in Next.js Pages Router (`pages/index.tsx`)
DESCRIPTION: This `getStaticProps` function demonstrates how to provide internationalized messages on a page level for a Next.js Pages Router application when only a single language is supported. It hardcodes the `locale` to 'en' and imports messages from the corresponding JSON file, making them available as `pageProps.messages`. This ensures consistent message loading for the specified single language.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/pages-router.mdx#_snippet_3

LANGUAGE: js
CODE:
```
export async function getStaticProps() {
  const locale = 'en';

  return {
    props: {
      // You can get the messages from anywhere you like. The recommended pattern
      // is to put them in JSON files separated by locale (e.g. `en.json`).
      messages: (await import(`../../messages/${locale}.json`)).default
    }
  };
}
```

----------------------------------------

TITLE: Accessing Configured 'Now' Value with `useNow` and `getNow` in Next.js
DESCRIPTION: Explains how to retrieve the globally configured 'now' value using `useNow` for client components and `getNow` for async server components. This is useful for consistent relative date/time formatting, even if a global `now` isn't explicitly set (it defaults to current time).
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_25

LANGUAGE: tsx
CODE:
```
// Regular components
import {useNow} from 'next-intl';
const now = useNow();

// Async Server Components
import {getNow} from 'next-intl/server';
const now = await getNow();
```

----------------------------------------

TITLE: Configuring Crowdin Translation Files (YAML)
DESCRIPTION: This YAML configuration file (`crowdin.yml`) instructs Crowdin on how to locate source translation files and where to place the corresponding translated files within the repository. It specifies that `en.json` is the source and `%{locale}.json` will be the translation output.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/localization-management.mdx#_snippet_1

LANGUAGE: YAML
CODE:
```
files:
  - source: /messages/en.json
    translation: /messages/%locale%.json
```

----------------------------------------

TITLE: Updating NextIntlClientProvider Import Path
DESCRIPTION: This snippet shows the updated import path for `NextIntlClientProvider`. Previously, it was imported from `next-intl/client`, but now it should be imported directly from `next-intl`. This change simplifies the import statement and aligns with the new module export structure.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-3-0.mdx#_snippet_2

LANGUAGE: diff
CODE:
```
- import {NextIntlClientProvider} from 'next-intl/client';
+ import {NextIntlClientProvider} from 'next-intl';
```

----------------------------------------

TITLE: Transforming Flat Keys to Nested Object - JavaScript
DESCRIPTION: This JavaScript snippet demonstrates how to transform a flat object with dot-separated keys into a deeply nested object. It uses `Object.entries` and `reduce` with a `set` utility (presumably from a library like `lodash/set` or a custom implementation) to build the hierarchical structure.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_8

LANGUAGE: javascript
CODE:
```
const output = Object.entries(input).reduce(
  (acc, [key, value]) => set(acc, key, value),
  {}
);
```

----------------------------------------

TITLE: Stabilizing `setRequestLocale` Import in next-intl
DESCRIPTION: This snippet shows the change in the import statement for the locale setting function. The `unstable_setRequestLocale` API has been renamed to `setRequestLocale`, indicating its stabilization and readiness for general use in `next-intl` server components.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-3-22.mdx#_snippet_9

LANGUAGE: Diff
CODE:
```
- import {unstable_setRequestLocale} from 'next-intl/server';
+ import {setRequestLocale} from 'next-intl/server';
```

----------------------------------------

TITLE: Customizing next-intl Request File Path in next.config.ts (JavaScript)
DESCRIPTION: This snippet shows how to specify a custom path for the `next-intl` request configuration file (`i18n/request.ts`) within `next.config.ts`. By passing a string argument to `createNextIntlPlugin`, developers can override the default file location, allowing for more flexible project structures.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/configuration.mdx#_snippet_1

LANGUAGE: JavaScript
CODE:
```
const withNextIntl = createNextIntlPlugin(
  // Specify a custom path here
  './somewhere/else/request.ts'
);
```

----------------------------------------

TITLE: Defining Nested Input Object for Key Transformation - JavaScript
DESCRIPTION: This snippet defines an input object with dot-separated keys, representing a flat structure that will be transformed into a nested object. It serves as the source data for the key transformation process.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_7

LANGUAGE: javascript
CODE:
```
const input = {
  'one.one': '1.1',
  'one.two': '1.2',
  'two.one.one': '2.1.1'
};
```

----------------------------------------

TITLE: Forcing Locale Prefix on Redirect with next-intl (TypeScript)
DESCRIPTION: Explains the `forcePrefix` option for the `redirect` function. Setting this to `true` ensures that the locale prefix is included in the redirected URL, regardless of the `localePrefix` setting, which is useful for updating the locale cookie.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_15

LANGUAGE: TypeScript
CODE:
```
// Will initially redirect to `/en/about` to update the locale
// cookie, regardless of your `localePrefix` setting
redirect({href: '/about', locale: 'en', forcePrefix: true});
```

----------------------------------------

TITLE: Rendering Raw HTML Markup with t.markup (next-intl, JavaScript)
DESCRIPTION: This snippet demonstrates the use of `t.markup` for scenarios where you need to emit raw HTML markup directly. Unlike `t.rich`, the functions provided to `t.markup` accept and return strings, allowing for string-based HTML manipulation.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_28

LANGUAGE: json
CODE:
```
{
  "markup": "This is <important>important</important>"
}
```

LANGUAGE: javascript
CODE:
```
// Returns 'This is <b>important</b>'
t.markup('markup', {
  important: (chunks) => `<b>${chunks}</b>`
});
```

----------------------------------------

TITLE: Upgrading Next-Intl to Latest Version
DESCRIPTION: This command is used to upgrade the `next-intl` package to its latest available version using npm. Running this command will fetch and install the most recent release, incorporating all the new features, bug fixes, and breaking changes discussed in the upgrade guide.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-3-0.mdx#_snippet_4

LANGUAGE: shell
CODE:
```
npm install next-intl@latest
```

----------------------------------------

TITLE: Retrieving Messages with Escaped Curly Braces (JavaScript)
DESCRIPTION: This JavaScript snippet shows how to call the `t` (translation) function to retrieve a message containing escaped curly braces. It demonstrates that the `next-intl` runtime correctly processes the escaped curly braces, rendering them as literal characters in the output string. This confirms the effectiveness of the single quote escaping mechanism.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_22

LANGUAGE: js
CODE:
```
t('message'); // "Escape curly braces with single quotes (e.g. {name})"
```

----------------------------------------

TITLE: Disabling next-intl localeCookie (TSX)
DESCRIPTION: This snippet shows how to completely disable the `localeCookie` feature in `next-intl`. By setting the `localeCookie` option to `false` within the `defineRouting` configuration, the library will not set a cookie to remember the user's locale preference.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_15

LANGUAGE: tsx
CODE:
```
import {defineRouting} from 'next-intl/routing';

export const routing = defineRouting({
  // ...

  localeCookie: false
});
```

----------------------------------------

TITLE: Formatting Relative Dates with date-fns in Next.js
DESCRIPTION: This component demonstrates how to format a date relative to the current time using `date-fns`'s `formatDistance` function within a Next.js application. It takes a `published` date as input and calculates the distance from `now`, which is instantiated using `new Date()`. The snippet implicitly raises the question of whether this approach is suitable for production due to potential inconsistencies between server and client environments.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/date-formatting-nextjs.mdx#_snippet_0

LANGUAGE: TSX
CODE:
```
import {formatDistance} from 'date-fns';

type Props = {
  published: Date;
};

export default function BlogPostPublishedDate({published}: Props) {
  const now = new Date();

  // ... is this ok? 🤔
  return <p>{formatDistance(published, now, {addSuffix: true})}</p>;
}
```

----------------------------------------

TITLE: Example HTML Output for Formatted React Element List
DESCRIPTION: Shows the expected HTML output when formatting an array of React elements using `next-intl`'s `format.list` function. This demonstrates how the elements are rendered within a paragraph tag with appropriate list separators.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/lists.mdx#_snippet_2

LANGUAGE: HTML
CODE:
```
<p>
  <a href="/user/1">Alice</a>, <a href="/user/2">Bob</a>, and
  <a href="/user/3">Charlie</a>
</p>
```

----------------------------------------

TITLE: Migrating `getRequestConfig` to `requestLocale` in Next.js 15 (TypeScript)
DESCRIPTION: This snippet demonstrates the migration of `getRequestConfig` to use the new `requestLocale` API, which is asynchronous and may return `undefined`. It shows how to await the locale, provide a fallback, and return the validated locale from the configuration. This change prepares for Next.js 15's async request APIs.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-3-22.mdx#_snippet_3

LANGUAGE: diff
CODE:
```
+ import {routing} from './i18n/routing';

export default getRequestConfig(async ({
-  locale
+  requestLocale
}) => {
+  // This typically corresponds to the `[locale]` segment
+  let locale = await requestLocale;

-  // Validate that the incoming `locale` parameter is valid
-  if (!routing.locales.includes(locale as any)) notFound();
+  // Ensure that the incoming locale is valid
+  if (!locale || !routing.locales.includes(locale as any)) {
+    locale = routing.defaultLocale;
+  }

  return {
+    locale,
    // ...
  };
});
```

----------------------------------------

TITLE: Dynamically Importing Localized MDX in Next.js App Router
DESCRIPTION: This snippet demonstrates how to dynamically import an MDX file based on the current locale within a Next.js App Router `page.tsx` file. It uses `import()` with a template literal to load the correct locale-specific MDX file and renders its default export. If the import fails (e.g., file not found for the locale), it uses `notFound()` from `next/navigation`.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/mdx.mdx#_snippet_0

LANGUAGE: tsx
CODE:
```
import {notFound} from 'next/navigation';

export default async function HomePage({params}) {
  const {locale} = await params;

  try {
    const Content = (await import(`./${locale}.mdx`)).default;
    return <Content />;
  } catch (error) {
    notFound();
  }
}
```

----------------------------------------

TITLE: Installing next-intl Package - Shell
DESCRIPTION: This command installs the `next-intl` package, which is a prerequisite for integrating internationalization capabilities into a Next.js application. It adds the package to the project's dependencies.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/without-i18n-routing.mdx#_snippet_0

LANGUAGE: sh
CODE:
```
npm install next-intl
```

----------------------------------------

TITLE: Preprocessing Locales for next-intl Selection (JavaScript)
DESCRIPTION: This JavaScript snippet illustrates how to preprocess a locale string before passing it to `next-intl`'s translation function. It specifically shows replacing dashes with underscores (`replaceAll('-', '_')`) to ensure compatibility with the `select` argument, which only supports alphanumeric characters and underscores for its values. This is crucial for correctly matching locale identifiers like 'en-GB' to their corresponding message parts.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_20

LANGUAGE: js
CODE:
```
const locale = 'en-GB';
t('message', {locale: locale.replaceAll('-', '_')});
```

----------------------------------------

TITLE: Minimal Root Layout for Static Export in Next.js App Router
DESCRIPTION: This snippet provides a minimal root layout for Next.js applications utilizing the App Router with static export. It simply passes through its children, fulfilling the requirement for a root layout even when no specific layout logic is needed, which is often the case for static exports.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/middleware.mdx#_snippet_10

LANGUAGE: TypeScript
CODE:
```
export default function RootLayout({children}) {
  return children;
}
```

----------------------------------------

TITLE: Example Localized MDX Content File
DESCRIPTION: This snippet shows an example of an MDX file (`en.mdx`) that can be used for localized content. It demonstrates importing a React component (`Portrait`) and using standard Markdown syntax (like `# Home`). Components that use `next-intl` hooks can be seamlessly included and will respect the locale.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/mdx.mdx#_snippet_1

LANGUAGE: mdx
CODE:
```
import Portrait from '@/components/Portrait';

# Home

Welcome to my site!

<Portrait />
```

----------------------------------------

TITLE: Minimal Root Layout for `app/not-found.tsx` in Next.js
DESCRIPTION: This minimal `RootLayout` component is required when a root `app/not-found.tsx` page is present in a Next.js App Router application. It simply passes its `children` prop through, fulfilling the prerequisite for the root 404 page to render.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/error-files.mdx#_snippet_3

LANGUAGE: tsx
CODE:
```
// Since we have a root `not-found.tsx` page, a layout file
// is required, even if it's just passing children through.
export default function RootLayout({children}) {
  return children;
}
```

----------------------------------------

TITLE: Customizing next-intl Request Configuration File Path
DESCRIPTION: This `next.config.ts` snippet demonstrates how to specify a custom path for the 'next-intl' request configuration file (conventionally `i18n/request.ts`). By passing a path to `createNextIntlPlugin`, you can organize your i18n files outside the default locations.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/with-i18n-routing.mdx#_snippet_9

LANGUAGE: js
CODE:
```
const withNextIntl = createNextIntlPlugin(
  // Specify a custom path here
  './somewhere/else/request.ts'
);
```

----------------------------------------

TITLE: Installing next-intl package
DESCRIPTION: This command installs the `next-intl` package, which is required for internationalization features in a Next.js application. It should be run in the project's root directory after creating a Next.js app.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/with-i18n-routing.mdx#_snippet_0

LANGUAGE: sh
CODE:
```
npm install next-intl
```

----------------------------------------

TITLE: Implementing an Expandable Client Component with Translated Props
DESCRIPTION: This Client Component (`'use client'`) receives `title` and `children` as props, which are expected to be translated content from a Server Component. It manages its own `expanded` state using `useState` to toggle visibility of its children, demonstrating client-side interactivity with server-side translated data.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/server-client-components.mdx#_snippet_5

LANGUAGE: tsx
CODE:
```
'use client';

import {useState} from 'react';

function Expandable({title, children}) {
  const [expanded, setExpanded] = useState(false);

  function onToggle() {
    setExpanded(!expanded);
  }

  return (
    <div>
      <button onClick={onToggle}>{title}</button>
      {expanded && <div>{children}</div>}
    </div>
  );
}
```

----------------------------------------

TITLE: Redirecting Root Path to Default Locale for Static Export
DESCRIPTION: This snippet shows how to implement a root page redirect for Next.js applications using static export. When the base URL (`/`) is requested, it redirects the user to the default locale (e.g., `/en`), which is necessary because server-side locale negotiation is not available with static exports.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/middleware.mdx#_snippet_9

LANGUAGE: TypeScript
CODE:
```
import {redirect} from 'next/navigation';

// Redirect the user to the default locale when `/` is requested
export default function RootPage() {
  redirect('/en');
}
```

----------------------------------------

TITLE: Accessing Component-Scoped Messages in React (TypeScript)
DESCRIPTION: This TypeScript React component shows how to use the `useTranslations` hook from `next-intl` to access messages scoped to a specific component. By passing 'About' to `useTranslations`, the `t` function is configured to resolve messages within the 'About' namespace, allowing direct access to keys like 'title'.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_1

LANGUAGE: TypeScript
CODE:
```
import {useTranslations} from 'next-intl';

function About() {
  const t = useTranslations('About');
  return <h1>{t('title')}</h1>;
}
```

----------------------------------------

TITLE: Including Global Formats in AppConfig for Type Safety (TypeScript)
DESCRIPTION: This TypeScript snippet extends the `next-intl` `AppConfig` interface to include the `Formats` property, assigning it the type of the `formats` object. This integration enables `next-intl` to provide strict type validation for format names used throughout the application, enhancing compile-time safety.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/typescript.mdx#_snippet_16

LANGUAGE: ts
CODE:
```
import {formats} from '@/i18n/request';

declare module 'next-intl' {
  interface AppConfig {
    // ...
    Formats: typeof formats;
  }
}
```

----------------------------------------

TITLE: Augmenting Locale Type Without i18n Routing (TypeScript)
DESCRIPTION: This snippet demonstrates how to augment the `Locale` type in `AppConfig` when not using `next-intl`'s i18n routing. It defines a constant array of locales and uses it to infer the `Locale` type, providing type safety for locale values.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/typescript.mdx#_snippet_4

LANGUAGE: tsx
CODE:
```
// Potentially imported from a shared config
const locales = ['en', 'de'] as const;

declare module 'next-intl' {
  interface AppConfig {
    // ...
    Locale: (typeof locales)[number];
  }
}
```

----------------------------------------

TITLE: Displaying Right-to-Left Text Example (Plain Text)
DESCRIPTION: Provides a simple example of Arabic text, demonstrating how right-to-left script appears. This snippet is presented within an HTML `div` with `dir="rtl"` to illustrate the visual effect of RTL text rendering.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_34

LANGUAGE: text
CODE:
```
النص في اللغة العربية _مثلا_ يُقرأ من اليمين لليسار
```

----------------------------------------

TITLE: Example Output of i18n-check (Bash)
DESCRIPTION: This snippet shows an example output from `i18n-check` when missing keys are detected. It clearly indicates the file and the specific key that is missing, helping developers identify and fix translation gaps.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/messages.mdx#_snippet_1

LANGUAGE: bash
CODE:
```
Found missing keys!
┌────────────────────┬───────────────────────────────┐
│ file               │ key                           │
├────────────────────┼───────────────────────────────┤
│  messages/de.json  │  NewsArticle.title            │
└────────────────────┴───────────────────────────────┘
```

----------------------------------------

TITLE: Example of Transformed Nested Object Output - JSON
DESCRIPTION: This JSON snippet illustrates the expected output structure after transforming the flat input object. It shows how dot-separated keys are converted into a hierarchical, nested object, removing redundancy and improving readability for message keys.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_9

LANGUAGE: json
CODE:
```
{
  "one": {
    "one": "1.1",
    "two": "1.2"
  },
  "two": {
    "one": {
      "one": "2.1.1"
    }
  }
}
```

----------------------------------------

TITLE: Upgrading next-intl to v4.0
DESCRIPTION: This command upgrades the `next-intl` package to version 4.0 using npm. It should be run after initially upgrading to the latest v3.x and checking for deprecation warnings to ensure a smooth transition.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-4-0.mdx#_snippet_11

LANGUAGE: Shell
CODE:
```
npm install next-intl@4
```

----------------------------------------

TITLE: Defining a Static Message Key - JSON
DESCRIPTION: This JSON snippet defines a simple, static message key 'message' with a fixed string value. This type of message is used directly without any dynamic interpolation.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage/messages.mdx#_snippet_10

LANGUAGE: json
CODE:
```
"message": "Hello world!"
```

----------------------------------------

TITLE: Defining i18n Routing Configuration
DESCRIPTION: This snippet defines the routing configuration for `next-intl`, specifying supported locales and a default locale. This configuration is shared between middleware and navigation APIs to handle language prefixes transparently, ensuring consistent i18n routing.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/with-i18n-routing.mdx#_snippet_4

LANGUAGE: ts
CODE:
```
import {defineRouting} from 'next-intl/routing';

export const routing = defineRouting({
  // A list of all locales that are supported
  locales: ['en', 'de'],

  // Used when no locale matches
  defaultLocale: 'en'
});
```

----------------------------------------

TITLE: Configuring next-intl Plugin - JavaScript
DESCRIPTION: This configuration sets up the `next-intl` plugin in `next.config.js`. It requires `createNextIntlPlugin` and wraps the existing Next.js configuration, enabling `next-intl` to create an alias for request-specific i18n configuration in Server Components.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router/without-i18n-routing.mdx#_snippet_3

LANGUAGE: js
CODE:
```
const createNextIntlPlugin = require('next-intl/plugin');

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig = {};

module.exports = withNextIntl(nextConfig);
```

----------------------------------------

TITLE: Importing PartnerContentLink Component in Next.js
DESCRIPTION: This snippet imports the `PartnerContentLink` component from a local path, likely used for rendering partner-related content. It demonstrates a standard ES module import syntax within a Next.js application, utilizing path aliases for cleaner imports.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/design-principles.mdx#_snippet_0

LANGUAGE: TypeScript
CODE:
```
import PartnerContentLink from '@/components/PartnerContentLink';
```

----------------------------------------

TITLE: Local Testing of Domain-Based Routing in next-intl
DESCRIPTION: Shows how to adapt the `domains` configuration conditionally based on the environment (`NODE_ENV`) to use `localhost` with different ports for local testing of domain-based routing setups.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_10

LANGUAGE: tsx
CODE:
```
import {defineRouting} from 'next-intl/routing';

const isDev = process.env.NODE_ENV === 'development';

export const routing = defineConfig({
  // ...
  domains: [
    {
      domain: isDev ? 'localhost:3000' : 'us.example.com'
      // ...
    },
    {
      domain: isDev ? 'localhost:3001' : 'ca.example.com'
      // ...
    }
  ]
});
```

----------------------------------------

TITLE: Running next-intl Dev Server for Domain Testing
DESCRIPTION: Provides shell commands to run the next-intl development server on specific ports (`3000`, `3001`) to simulate different domain configurations (`us.example.com`, `ca.example.com`) for local testing.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_11

LANGUAGE: sh
CODE:
```
# Like `us.example.com`
PORT=3000 npm run dev

# Like `ca.example.com`
PORT=3001 npm run dev
```

----------------------------------------

TITLE: Disabling Automatic Locale Detection in next-intl
DESCRIPTION: Shows how to disable the automatic locale detection mechanism (based on `accept-language` header or cookie) by setting the `localeDetection` property to `false` in the `defineRouting` configuration.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing.mdx#_snippet_13

LANGUAGE: tsx
CODE:
```
import {defineRouting} from 'next-intl/routing';

export const routing = defineRouting({
  // ...
  localeDetection: false
});
```

----------------------------------------

TITLE: Migrating `Link` ComponentProps Type Argument (TypeScript)
DESCRIPTION: This snippet demonstrates a necessary change when migrating to `createNavigation` regarding the `Link` component's `ComponentProps` type. The generic `Pathname` type argument should no longer be provided, simplifying the type definition.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/next-intl-3-22.mdx#_snippet_5

LANGUAGE: diff
CODE:
```
- ComponentProps<typeof Link<Pathname>>
+ ComponentProps<typeof Link>
```

----------------------------------------

TITLE: Linking to Unknown Routes with Pathnames (TSX)
DESCRIPTION: Explains how to bypass the strict type checking imposed by the `pathnames` setting when needing to link to routes not defined in the configuration. This is achieved by using a `@ts-expect-error` comment on the `Link` component.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/routing/navigation.mdx#_snippet_7

LANGUAGE: tsx
CODE:
```
// @ts-expect-error
<Link href="/unknown">...</Link>
```

----------------------------------------

TITLE: Displaying i18n Routing Options with Card Components
DESCRIPTION: This JSX snippet uses `Cards` and `Card` components to present two distinct internationalization routing strategies for Next.js App Router. Each `Card` describes an option, including its title, a link to more details, and a brief explanation of its use case, guiding users to choose between prefixed pathnames or locale provision based on user settings.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router.mdx#_snippet_1

LANGUAGE: JSX
CODE:
```
<Cards className="mt-8 max-w-[550px]">
  <Card
    title="With i18n routing"
    href="/docs/getting-started/app-router/with-i18n-routing"
  >
    Uses a top-level `[locale]` segment for prefixed pathnames like `/en/about`
    or domain-based routing like `en.example.com/about`
  </Card>
  <Card
    title="Without i18n routing"
    href="/docs/getting-started/app-router/without-i18n-routing"
  >
    Useful if you'd like to provide a locale to `next-intl`, e.g. based on user
    settings, or if your app only supports a single language
  </Card>
</Cards>
```

----------------------------------------

TITLE: Importing Components in React
DESCRIPTION: This snippet demonstrates how to import custom React components (`Callout`, `Card`, `Cards`) from a local path (`@/components/`) into a file. These imports make the components available for use within the current module.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import Callout from '@/components/Callout';
import Card from '@/components/Card';
import Cards from '@/components/Cards';
```

----------------------------------------

TITLE: Next.js Error Boundary Schematic (TSX)
DESCRIPTION: This schematic illustrates how Next.js internally wraps pages with an error boundary when an `error.js` file is defined. It shows `RootLayout` enclosing an `ErrorBoundary` which then wraps the `Page` component, catching runtime errors. This setup ensures that errors within the page are gracefully handled by the specified `Error` fallback component.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments/error-files.mdx#_snippet_5

LANGUAGE: TSX
CODE:
```
<RootLayout>
  <ErrorBoundary fallback={<Error />}>
    <Page />
  </ErrorBoundary>
</RootLayout>
```

----------------------------------------

TITLE: Rendering Counter Component in Next.js (JSX)
DESCRIPTION: This snippet renders the `Counter` component within the page's JSX. As indicated by its path, it is likely a client-side interactive component, demonstrating how components are used in a Next.js page.
SOURCE: https://github.com/amannn/next-intl/blob/main/examples/example-app-router-playground/src/app/[locale]/about/de.mdx#_snippet_3

LANGUAGE: JSX
CODE:
```
<Counter />
```

----------------------------------------

TITLE: Installing Dependencies and Running React Native App with pnpm
DESCRIPTION: This snippet provides commands to install project dependencies using `pnpm` and then run the React Native application on Android, iOS, or web platforms. It highlights the use of `pnpm` for package management and execution, ensuring the necessary runtime requirements are met.
SOURCE: https://github.com/amannn/next-intl/blob/main/examples/example-react-native/README.md#_snippet_0

LANGUAGE: Shell
CODE:
```
pnpm install

# Run the app with one of:
pnpm run android
pnpm run ios
pnpm run web
```

----------------------------------------

TITLE: Rendering Cards Component with Props
DESCRIPTION: This snippet shows the usage of `Cards` and `Card` React components. The `Cards` component acts as a container, applying CSS classes for styling. Inside, a `Card` component is rendered with `title` and `href` props, likely for navigation and display.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/usage.mdx#_snippet_1

LANGUAGE: JSX
CODE:
```
<Cards className="mt-8 lg:w-1/2">
  <Card title="Render messages in components" href="/docs/usage/messages" />
</Cards>
```

----------------------------------------

TITLE: Rendering StayUpdated Component in Next.js
DESCRIPTION: This snippet renders the `StayUpdated` component, likely a call-to-action or subscription form, at the bottom of the blog page. It's a self-closing JSX tag, indicating a component that doesn't wrap child content.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/index.mdx#_snippet_3

LANGUAGE: JSX
CODE:
```
<StayUpdated />
```

----------------------------------------

TITLE: Running next-intl Documentation Locally with pnpm
DESCRIPTION: This snippet provides the necessary commands to install project dependencies and start the local development server for the `next-intl` documentation website. It utilizes `pnpm` as the package manager.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/README.md#_snippet_0

LANGUAGE: Shell
CODE:
```
pnpm install
pnpm dev
```

----------------------------------------

TITLE: Displaying Environment Links with Card Components
DESCRIPTION: This JSX snippet uses the `Cards` component as a container for multiple `Card` components. Each `Card` represents a specific environment where `next-intl` APIs are available, providing a title and a link to its respective documentation page. The `className` prop applies styling for margin and width.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments.mdx#_snippet_1

LANGUAGE: JSX
CODE:
```
<Cards className="mt-8 lg:w-1/2">
  <Card
    title="Server & Client Components"
    href="/docs/environments/server-client-components"
  />
  <Card
    title="Server Actions, Metadata & Route Handlers"
    href="/docs/environments/actions-metadata-route-handlers"
  />
  <Card
    title="Error files (e.g. not-found)"
    href="/docs/environments/error-files"
  />
  <Card title="Markdown (MDX)" href="/docs/environments/mdx" />
  <Card
    title="Core library (agnostic)"
    href="/docs/environments/core-library"
  />
</Cards>
```

----------------------------------------

TITLE: Importing AsyncComponent in Next.js (JavaScript)
DESCRIPTION: This snippet imports the `AsyncComponent` from a local path, making it available for use within the current file. This is a common pattern in Next.js for organizing and using components.
SOURCE: https://github.com/amannn/next-intl/blob/main/examples/example-app-router-playground/src/app/[locale]/about/de.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import AsyncComponent from '@/components/AsyncComponent';
```

----------------------------------------

TITLE: Installing Dependencies with pnpm
DESCRIPTION: This command installs all project dependencies and performs an initial build of the packages. It is a crucial step to set up the development environment after cloning the repository.
SOURCE: https://github.com/amannn/next-intl/blob/main/CONTRIBUTORS.md#_snippet_0

LANGUAGE: Shell
CODE:
```
pnpm install
```

----------------------------------------

TITLE: Rendering AsyncComponent in Next.js (JSX)
DESCRIPTION: This snippet renders the `AsyncComponent` within the page's JSX. In a Next.js context, this component could be a server component or a client component, depending on its definition and usage.
SOURCE: https://github.com/amannn/next-intl/blob/main/examples/example-app-router-playground/src/app/[locale]/about/de.mdx#_snippet_2

LANGUAGE: JSX
CODE:
```
<AsyncComponent />
```

----------------------------------------

TITLE: Ignoring Generated Message Declaration Files in Git
DESCRIPTION: This `.gitignore` entry specifies that automatically generated `.d.json.ts` files within the `messages` directory should be ignored by Git. This practice helps maintain a clean version control history by excluding transient build artifacts.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/typescript.mdx#_snippet_13

LANGUAGE: text
CODE:
```
messages/*.d.json.ts
```

----------------------------------------

TITLE: Importing Counter Component in Next.js (JavaScript)
DESCRIPTION: This snippet imports the `Counter` component, which is likely a client-side component, from a specific relative path. This pattern is typical for structuring components in a Next.js application.
SOURCE: https://github.com/amannn/next-intl/blob/main/examples/example-app-router-playground/src/app/[locale]/about/de.mdx#_snippet_1

LANGUAGE: JavaScript
CODE:
```
import Counter from '@/components/client/02-MessagesOnClientCounter/Counter';
```

----------------------------------------

TITLE: Running ESLint and Prettier via Command Line (Shell)
DESCRIPTION: This shell script navigates into the `next-intl` package directory and then executes `pnpm` commands to run ESLint for code linting with automatic fixes and Prettier for code formatting with automatic writing. It's an alternative to editor integrations for maintaining code quality.
SOURCE: https://github.com/amannn/next-intl/blob/main/CONTRIBUTORS.md#_snippet_2

LANGUAGE: sh
CODE:
```
cd packages/next-intl
pnpm eslint src --fix
pnpm prettier src --write
```

----------------------------------------

TITLE: Conditional ESLint Rule for CI Performance (JavaScript)
DESCRIPTION: This ESLint configuration snippet demonstrates how to conditionally enable expensive, type-aware linting rules, such as `@typescript-eslint/no-misused-promises`, only when running in a Continuous Integration (CI) environment. This helps optimize editor performance during development.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/typescript.mdx#_snippet_8

LANGUAGE: tsx
CODE:
```
// ...

  // Run expensive, type-aware linting only on CI
  '@typescript-eslint/no-misused-promises': process.env.CI
    ? 'error'
    : 'off'
```

----------------------------------------

TITLE: Importing UI Components in Next.js
DESCRIPTION: This snippet demonstrates importing custom React components, `Card` and `Cards`, from a local components directory. These components are likely used for structuring and displaying information within a Next.js application, promoting reusability and modularity.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import Card from '@/components/Card';
import Cards from '@/components/Cards';
```

----------------------------------------

TITLE: Focusing Individual Tests with it.only
DESCRIPTION: This method, available in Vitest and Playwright test setups, allows developers to run only a specific test suite or test case during development. It is useful for isolating and debugging individual tests.
SOURCE: https://github.com/amannn/next-intl/blob/main/CONTRIBUTORS.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
it.only
```

----------------------------------------

TITLE: Running the Example Locally (NPM)
DESCRIPTION: This snippet provides the commands to install project dependencies and start the development server for the Remix example application using npm. These steps are essential to get the application running on a local machine.
SOURCE: https://github.com/amannn/next-intl/blob/main/examples/example-remix/README.md#_snippet_0

LANGUAGE: Shell
CODE:
```
npm install
npm run dev
```

----------------------------------------

TITLE: Importing UI Components in Next.js
DESCRIPTION: This JavaScript snippet demonstrates importing essential UI components for a Next.js application. It includes custom components like `PartnerContentLink`, `Callout`, and `Screenshot` from a local components directory, alongside Next.js's built-in `Image` component, preparing them for use in rendering the page content.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows/localization-management.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import PartnerContentLink from '@/components/PartnerContentLink';
import Callout from '@/components/Callout';
import Screenshot from '@/components/Screenshot';
import Image from 'next/image';
```

----------------------------------------

TITLE: Importing BlogPostLink Component in Next.js
DESCRIPTION: This snippet imports the `BlogPostLink` React component, which is used to render individual blog post links on the page. It's sourced from the local `@/components` directory, indicating a standard Next.js component structure.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/index.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import BlogPostLink from '@/components/BlogPostLink';
```

----------------------------------------

TITLE: Importing Example Component in JavaScript
DESCRIPTION: This snippet imports the `Example` React component, likely used for rendering structured example cards, from its module path. This component serves as a visual wrapper for showcasing different `next-intl` integration scenarios.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/examples.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import Example from '@/components/Example';
```

----------------------------------------

TITLE: Importing StayUpdated Component in Next.js
DESCRIPTION: This snippet imports the `StayUpdated` component, which is likely an MDX component used to display information or a form for users to subscribe to updates. It's sourced from the local `@/components` directory.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/index.mdx#_snippet_1

LANGUAGE: JavaScript
CODE:
```
import StayUpdated from '@/components/StayUpdated.mdx';
```

----------------------------------------

TITLE: Importing UI Components for Page Layout
DESCRIPTION: This snippet imports custom React components (Card, Cards, Callout, Steps, Details) from the local `@/components` directory. These components are essential for structuring and displaying content within the Next.js application, providing a consistent UI for documentation and feature presentation.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/getting-started/app-router.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import Card from '@/components/Card';
import Cards from '@/components/Cards';
import Callout from '@/components/Callout';
import Steps from '@/components/Steps';
import Details from '@/components/Details';
```

----------------------------------------

TITLE: Importing UI Components for Layout
DESCRIPTION: This snippet imports `Card` and `Cards` components from the local `@/components` directory. These components are likely used for structuring and displaying content, such as navigation links or feature descriptions, within the documentation page.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/environments.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import Card from '@/components/Card';
import Cards from '@/components/Cards';
```

----------------------------------------

TITLE: Displaying Next-Intl Workflows with Card Components
DESCRIPTION: This JSX snippet utilizes custom `Cards` and `Card` components to present various `next-intl` integrations and workflows. Each `Card` specifies a title and a hyperlink, directing users to detailed documentation pages for each integration, such as TypeScript augmentation or Crowdin localization.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/docs/workflows.mdx#_snippet_1

LANGUAGE: JSX
CODE:
```
<Cards className="mt-8 lg:w-1/2">
  <Card title="TypeScript augmentation" href="/docs/workflows/typescript" />
  <Card
    title="Localization management with Crowdin"
    href="/docs/workflows/localization-management"
  />
  <Card title="VSCode integration" href="/docs/workflows/vscode-integration" />
  <Card title="ESLint" href="/docs/workflows/linting" />
  <Card title="Storybook" href="/docs/workflows/storybook" />
</Cards>
```

----------------------------------------

TITLE: Rendering Blog Post Links with JSX in Next.js
DESCRIPTION: This JSX snippet renders a flexible column layout containing multiple `BlogPostLink` components. Each `BlogPostLink` displays a specific blog post's title, date, and author, linking to its respective article. This structure efficiently presents a list of blog entries.
SOURCE: https://github.com/amannn/next-intl/blob/main/docs/src/pages/blog/index.mdx#_snippet_2

LANGUAGE: JSX
CODE:
```
<div className="flex flex-col gap-4 py-8">\n  <BlogPostLink\n    href="/blog/next-intl-4-0"\n    title="next-intl 4.0"\n    date="Mar 12, 2025"\n    author="By Jan Amann"\n  />\n  <BlogPostLink\n    href="/blog/next-intl-3-22"\n    title="next-intl 3.22: Incrementally moving forward"\n    date="Oct 21, 2024"\n    author="By Jan Amann"\n  />\n  <BlogPostLink\n    href="/blog/date-formatting-nextjs"\n    title="Reliable date formatting in Next.js"\n    date="Sep 25, 2024"\n    author="By Jan Amann"\n  />\n  <BlogPostLink\n    href="/blog/next-intl-3-0"\n    title="next-intl 3.0"\n    date="Nov 14, 2023"\n    author="By Jan Amann"\n  />\n  <BlogPostLink\n    href="/blog/translations-outside-of-react-components"\n    title="How (not) to use translations outside of React components"\n    date="Apr 21, 2023"\n    author="By Jan Amann"\n  />\n</div>
```

----------------------------------------

TITLE: Importing Components in Next.js
DESCRIPTION: This snippet demonstrates how to import client and async components into a Next.js application using ES module syntax. These imports make the components available for rendering within the application's pages or other components.
SOURCE: https://github.com/amannn/next-intl/blob/main/examples/example-app-router-playground/src/app/[locale]/about/en.mdx#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import AsyncComponent from '@/components/AsyncComponent';
import Counter from '@/components/client/02-MessagesOnClientCounter/Counter';
```