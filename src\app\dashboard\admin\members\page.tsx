"use client";

import { MemberTable, MemberTableBusinessRules } from "@/components/shared/member-table";
import { <PERSON>K<PERSON> } from "@/lib/rbac/rbac-utils";
import { PageProps } from "@/types/app/PageProps";
import { useTranslations } from 'next-intl';
import { useRbacPermission } from "@/hooks/use-rbac-permission";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

const OrgAdminMembersPage = (_props: PageProps) => {
  const t = useTranslations('AdminMembersPage');
  const rbac = useRbacPermission();
  const router = useRouter();

  // Check permissions on client side
  useEffect(() => {
    if (!rbac.checkPermission({ rMinRole: "orgAdmin" })) {
      router.push("/dashboard");
    }
  }, [rbac, router]);

  // Define business rules for OrgAdmins managing their members
  // OrgAdmins have full permissions for all actions within their organization
  // SECURITY: Additional safeguard - restrict role assignments to orgAdmin level and below
  const orgAdminBusinessRules: MemberTableBusinessRules = {
    profileEditMinRole: "orgAdmin", // OrgAdmin+ can view/edit profiles
    roleEditMinRole: "orgAdmin", // OrgAdmin+ can change roles
    maxAssignableRole: "orgAdmin", // SAFEGUARD: Cannot assign roles above orgAdmin (prevents superAdmin/supportAdmin assignment)
    statusChangeMinRole: "orgAdmin", // OrgAdmin+ can change status
    deleteMinRole: "orgAdmin", // OrgAdmin+ can delete members
    inviteMinRole: "orgAdmin", // OrgAdmin+ can invite new members
  };

  const orgAdminRoleFilters: RoleKey[] = ['orgAdmin', 'orgMember', 'orgAccounting', 'orgClient'];

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">{t('title')}</h1>
      <MemberTable
        organizationIdScope="CURRENT_CONTEXT"
        roleFilters={orgAdminRoleFilters}
        permissionModel="role-based"
        businessRules={orgAdminBusinessRules}
        showOrganizationColumn={false}
        tableTitle={t('tableTitle')}
        // onInviteUser prop omitted for now.
        // If MemberTable shows an invite button based on businessRules.inviteMinRole,
        // it would need an actual handler (client-side navigation or dialog management).
      />
    </div>
  );
};

export default OrgAdminMembersPage;