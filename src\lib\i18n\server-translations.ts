import { getTranslations } from 'next-intl/server';

/**
 * Get translations for server actions and server components
 * This is a helper to make it easier to use translations in server-side code
 */
export async function getServerTranslations(namespace?: string) {
  try {
    return await getTranslations(namespace);
  } catch (error) {
    console.error('[getServerTranslations] Failed to load translations:', error);
    // Return a fallback function that returns the key if translations fail
    return (key: string, values?: Record<string, any>) => {
      if (values) {
        // Simple string replacement for fallback
        return key.replace(/\{(\w+)\}/g, (match, prop) => values[prop] || match);
      }
      return key;
    };
  }
}

/**
 * Get validation error translations for server actions
 */
export async function getValidationTranslations() {
  return await getServerTranslations('ServerActions.validation');
}

/**
 * Get server action error translations
 */
export async function getServerActionTranslations(namespace: string) {
  return await getServerTranslations(`ServerActions.${namespace}`);
}
