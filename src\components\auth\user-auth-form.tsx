"use client";

import * as React from "react";
import { createClient } from "@/lib/supabase/client";
import { Check<PERSON>ircle, Mail, Clock } from "lucide-react";
import { useTranslations } from 'next-intl';

import { cn } from "@/lib/utils";
import { Icons } from "@/components/icons";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { validateEmailFormat, RATE_LIMITS } from "@/lib/auth/validation";
import { TurnstileCaptcha, useTurnstile } from "@/components/auth/turnstile-captcha";
import { useToastMessages } from "@/hooks/use-toast-messages";
import type { UserAuthFormProps } from "@/types/components/auth/UserAuthFormProps"; // Import the interface

// UserAuthFormProps interface removed, now imported

export function UserAuthForm({
  className,
  redirectTo = "/dashboard",
  urlError = null,
  onEmailSentChange,
  ...props
}: UserAuthFormProps) {
  const t = useTranslations('Auth.form');
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [emailSent, setEmailSent] = React.useState<boolean>(false);
  const [error, setError] = React.useState<string | null>(null);
  const [email, setEmail] = React.useState<string>("");
  const [emailError, setEmailError] = React.useState<string | null>(null);
  const [cooldownTime, setCooldownTime] = React.useState<number>(0);
  const [lastRequestTime, setLastRequestTime] = React.useState<number>(0);
  const [showEmailForm, setShowEmailForm] = React.useState<boolean>(false);
  const [isEmailValid, setIsEmailValid] = React.useState<boolean>(false);

  // Toast messages hook
  const toastMessages = useToastMessages();

  // CAPTCHA state management
  const {
    captchaToken,
    captchaError,
    captchaExpired,
    isRetrying,
    handleSuccess: handleCaptchaSuccess,
    handleError: handleCaptchaError,
    handleExpire: handleCaptchaExpire,
    reset: resetCaptcha,
  } = useTurnstile();

  // Handle URL errors on component mount
  React.useEffect(() => {
    if (urlError) {
      // Use requestAnimationFrame to ensure DOM is ready, then add a small delay
      let timer: NodeJS.Timeout;
      const rafId = requestAnimationFrame(() => {
        timer = setTimeout(() => {
          // Map specific error messages to appropriate toast messages
          if (urlError.toLowerCase().includes('expired')) {
            toastMessages.auth.magicLinkExpired();
          } else if (urlError.toLowerCase().includes('invalid')) {
            toastMessages.auth.magicLinkInvalid();
          } else if (urlError.toLowerCase().includes('authentication failed')) {
            toastMessages.auth.authenticationFailed(urlError);
          } else {
            toastMessages.auth.loginError(urlError);
          }
        }, 100); // Small delay after DOM is ready
      });

      return () => {
        cancelAnimationFrame(rafId);
        if (timer) clearTimeout(timer);
      };
    }
  }, [urlError]);

  // Notify parent component when emailSent state changes
  React.useEffect(() => {
    onEmailSentChange?.(emailSent);
  }, [emailSent, onEmailSentChange]);

  // Cooldown timer effect
  React.useEffect(() => {
    let interval: NodeJS.Timeout;

    if (cooldownTime > 0) {
      interval = setInterval(() => {
        setCooldownTime((prev) => {
          if (prev <= 1) {
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [cooldownTime]);

  // Email validation handler
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newEmail = e.target.value;
    setEmail(newEmail);
    setEmailError(null);
    setIsEmailValid(false);

    if (newEmail.trim()) {
      const validation = validateEmailFormat(newEmail);
      if (!validation.isValid) {
        setEmailError(validation.error || t('invalidEmailFormat'));
        setIsEmailValid(false);
      } else {
        setIsEmailValid(true);
      }
    }
  };

  async function onSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault();
    setIsLoading(true);
    setError(null);
    setEmailError(null);

    const formData = new FormData(event.currentTarget);
    const emailValue = formData.get("email") as string;

    // Client-side validation
    const validation = validateEmailFormat(emailValue);
    if (!validation.isValid) {
      setEmailError(validation.error || t('invalidEmailFormat'));
      setIsLoading(false);
      return;
    }

    // CAPTCHA validation is handled by Supabase automatically
    // We'll still send the token, but Supabase will validate it

    // Check cooldown
    const now = Date.now();
    const timeSinceLastRequest = now - lastRequestTime;
    if (timeSinceLastRequest < RATE_LIMITS.COOLDOWN_SECONDS * 1000) {
      const remainingCooldown = Math.ceil((RATE_LIMITS.COOLDOWN_SECONDS * 1000 - timeSinceLastRequest) / 1000);
      setCooldownTime(remainingCooldown);
      setError(t('pleaseWaitBeforeRequesting', { seconds: remainingCooldown }));
      setIsLoading(false);
      return;
    }

    try {
      // Use our secure API endpoint instead of direct Supabase call
      const response = await fetch('/api/auth/magic-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: emailValue,
          website: '', // Honeypot field
          userAgent: navigator.userAgent,
          timestamp: Date.now(),
          captchaToken: captchaToken, // Include CAPTCHA token
        }),
      });

      const result = await response.json();

      if (response.status === 429) {
        // Rate limited
        if (result.cooldownSeconds) {
          setCooldownTime(result.cooldownSeconds);
        }
        setError(result.message);
      } else if (response.status === 400 && result.message?.includes('CAPTCHA')) {
        // CAPTCHA-specific error
        setError(result.message);
        resetCaptcha(); // Reset CAPTCHA to allow retry
      } else if (result.success) {
        setEmailSent(true);
        setLastRequestTime(now);
        setCooldownTime(RATE_LIMITS.COOLDOWN_SECONDS);
        resetCaptcha(); // Reset CAPTCHA after successful submission
      } else {
        setError(result.message || t('failedToSendMagicLink'));
        // Don't reset CAPTCHA for generic errors as it might still be valid
      }
    } catch (error) {
      console.error('Magic link request failed:', error);
      setError(t('networkError'));
    } finally {
      setIsLoading(false);
    }
  }

  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);
      const supabase = createClient();
      const { error } = await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: `${
            window.location.origin
          }/auth/callback?redirectTo=${encodeURIComponent(redirectTo)}`,
          queryParams: {
            access_type: "offline",
            prompt: "consent",
          },
        },
      });

      if (error) throw error;
    } catch (error) {
      setError(
        error instanceof Error ? error.message : t('failedToSignInWithGoogle')
      );
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <div className={cn("space-y-6", className)} {...props}>
      {!emailSent ? (
        <>
          <div className="space-y-4">
            {/* Email Login Section - Expandable */}
            {!showEmailForm ? (
              <Button
                onClick={() => setShowEmailForm(true)}
                className="w-full h-11 bg-[#194852] hover:bg-[#194852]/90 text-white rounded-md font-normal"
              >
                <Mail className="mr-2 h-4 w-4" />
                {t('loginWithEmail')}
              </Button>
            ) : (
              /* Email Form - Replaces the button */
              <form onSubmit={onSubmit} className="space-y-3">
                <div>
                  <Label htmlFor="email" className="text-sm text-gray-600">
                    {t('enterEmailLabel')}
                  </Label>
                  <Input
                    id="email"
                    placeholder={t('emailPlaceholder')}
                    type="email"
                    autoCapitalize="none"
                    autoComplete="email"
                    autoCorrect="off"
                    disabled={isLoading || cooldownTime > 0}
                    name="email"
                    value={email}
                    onChange={handleEmailChange}
                    required
                    autoFocus
                    className={cn(
                      "mt-2 h-11 rounded-md border bg-white px-3 py-2 text-sm outline-none focus:ring-0",
                      emailError
                        ? "border-red-300 focus:border-red-400"
                        : "border-gray-200 focus:border-gray-400"
                    )}
                  />
                  {emailError && (
                    <p className="text-sm text-red-500 mt-1">{emailError}</p>
                  )}

                  {/* Honeypot field - hidden from users */}
                  <input
                    type="text"
                    name="website"
                    style={{ display: 'none' }}
                    tabIndex={-1}
                    autoComplete="off"
                  />
                </div>

                {/* Show Send Magic Link button only when email is valid */}
                {isEmailValid && (
                  <Button
                    disabled={isLoading || cooldownTime > 0 || !captchaToken}
                    className="w-full h-11 bg-[#194852] hover:bg-[#194852]/90 text-white rounded-md font-normal disabled:opacity-50"
                  >
                    {cooldownTime > 0 ? (
                      <>
                        <Clock className="mr-2 h-4 w-4" />
                        {t('waitSeconds', { seconds: cooldownTime })}
                      </>
                    ) : isLoading ? (
                      <>
                        <Mail className="mr-2 h-4 w-4 animate-spin" />
                        {t('sendingMagicLink')}
                      </>
                    ) : !captchaToken ? (
                      <>
                        <Mail className="mr-2 h-4 w-4" />
                        {t('completeSecurityFirst')}
                      </>
                    ) : (
                      <>
                        <Mail className="mr-2 h-4 w-4" />
                        {t('sendMagicLink')}
                      </>
                    )}
                  </Button>
                )}
              </form>
            )}

            {/* Separator */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-gray-200" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-white px-2 text-gray-500">{t('or')}</span>
              </div>
            </div>

            {/* Google Login Button */}
            <Button
              variant="outline"
              onClick={handleGoogleSignIn}
              disabled={isLoading || !captchaToken}
              className="w-full h-11 border border-gray-200 bg-white text-gray-700 hover:bg-gray-50 rounded-md font-normal"
            >
              <Icons.google className="mr-2 h-4 w-4" />
              {!captchaToken ? t('completeSecurityFirst') : t('continueWithGoogle')}
            </Button>

            {/* CAPTCHA Section - Separated with visual distinction */}
            <div className="pt-4">
              {/* Full-width separator line */}
              <div className="w-full border-t border-gray-200 mb-4"></div>

              {/* CAPTCHA with label */}
              <div className="space-y-3">
                <div className="text-center">
                  <p className="text-xs text-gray-500">CAPTCHA verification by Cloudflare</p>
                </div>

                <TurnstileCaptcha
                  onSuccess={handleCaptchaSuccess}
                  onError={handleCaptchaError}
                  onExpire={handleCaptchaExpire}
                  className="flex justify-center"
                />

                {captchaError && !isRetrying && (
                  <p className="text-sm text-red-500 text-center">
                    Security verification failed. Please try again.
                  </p>
                )}
                {isRetrying && (
                  <p className="text-sm text-blue-500 text-center">
                    Retrying security verification...
                  </p>
                )}
                {captchaExpired && (
                  <p className="text-sm text-orange-500 text-center">
                    Security verification expired. It will refresh automatically.
                  </p>
                )}
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="text-center space-y-4">
          <div className="text-green-600">
            <CheckCircle className="h-12 w-12 mx-auto mb-4" />
            <h3 className="text-lg font-medium">Check your email</h3>
            <p className="text-sm text-gray-600 mt-2">
              If you are an existing user you'll receive a login link shortly. Please also check your junk/spam folder before trying again.
            </p>
          </div>
          <Button
            variant="outline"
            onClick={() => {
              setEmailSent(false);
              setError(null);
              setEmailError(null);
              // Keep email form expanded and email value
              setIsEmailValid(!!email && !emailError); // Revalidate current email
              resetCaptcha(); // Reset CAPTCHA for new attempt
            }}
            disabled={cooldownTime > 0}
            className="w-full"
          >
            {cooldownTime > 0 ? `Wait ${cooldownTime}s` : 'Send another link'}
          </Button>
        </div>
      )}

      {error && <p className="text-sm text-red-500 mt-2">{error}</p>}
    </div>
  );
}
