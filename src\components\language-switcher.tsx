"use client";

import React from 'react';
import { useLocale } from '@/hooks/use-locale';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Globe } from 'lucide-react';

/**
 * Language switcher component for testing the translation system
 * This will be integrated into the TopBar later
 */
export function LanguageSwitcher() {
  const { locale, changeLocale, supportedLocales, localeNames, isLoading } = useLocale();

  if (isLoading) {
    return (
      <Button variant="ghost" size="sm" disabled>
        <Globe className="h-4 w-4" />
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm">
          <Globe className="h-4 w-4 mr-2" />
          {localeNames[locale]}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {supportedLocales.map((supportedLocale) => (
          <DropdownMenuItem
            key={supportedLocale}
            onClick={() => changeLocale(supportedLocale)}
            className={locale === supportedLocale ? 'bg-accent' : ''}
          >
            {localeNames[supportedLocale]}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
