"use client";

import React from 'react';
import { useLocale } from '@/hooks/use-locale';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Languages } from 'lucide-react';
import { useTranslations } from 'next-intl';

/**
 * Language switcher component for testing the translation system
 * This will be integrated into the TopBar later
 */
export function LanguageSwitcher() {
  const { locale, changeLocale, supportedLocales, isLoading } = useLocale();
  const t = useTranslations('Language');

  // Map locale codes to translation keys
  const getLocaleDisplayName = (localeCode: string) => {
    switch (localeCode) {
      case 'en': return t('english');
      case 'nl': return t('dutch');
      case 'th': return t('thai');
      default: return localeCode;
    }
  };

  if (isLoading) {
    return (
      <Button variant="outline" size="icon" disabled>
        <Languages className="h-4 w-4" />
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          <Languages className="h-4 w-4" />
          <span className="sr-only">{getLocaleDisplayName(locale)}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {supportedLocales.map((supportedLocale) => (
          <DropdownMenuItem
            key={supportedLocale}
            onClick={() => changeLocale(supportedLocale)}
            className={locale === supportedLocale ? 'bg-accent' : ''}
          >
            {getLocaleDisplayName(supportedLocale)}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
