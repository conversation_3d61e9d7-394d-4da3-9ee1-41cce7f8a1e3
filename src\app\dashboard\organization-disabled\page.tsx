"use client";

import React from "react";
import { useDashboard } from "@/components/providers/dashboard-provider";
import { useTranslations } from 'next-intl';
import Link from "next/link";

export default function OrganizationDisabledPage() {
  const { activeOrgName } = useDashboard();

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
        <h1 className="text-2xl font-bold mb-6 text-center">Organization Disabled</h1>
        
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-700">
            {activeOrgName ? (
              <>
                The organization &ldquo;{activeOrgName}&rdquo; has been disabled.
                Please use the organization switcher in the sidebar to switch to an active organization or contact AgencyForms to reactivate this organization.
              </>
            ) : (
              <>
                Your current organization has been disabled.
                Please use the organization switcher in the sidebar to switch to an active organization or contact AgencyForms for assistance.
              </>
            )}
          </p>
        </div>
        
        <div className="flex justify-center">
          <Link
            href="/auth/signout"
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md"
          >
            Sign Out
          </Link>
        </div>
      </div>
    </div>
  );
} 