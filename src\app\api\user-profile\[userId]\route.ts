import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'
import { canEditUserProfile } from '@/lib/permission-utils'
import { checkRbacPermission } from '@/lib/rbac/permissions-server'
import type { OrganizationMemberBasic } from '@/types/organization/OrganizationMember'

export async function GET(
  _request: Request,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Extract userId from params
    const resolvedParams = await params
    const { userId } = resolvedParams
    if (!userId) {
      return NextResponse.json({ error: 'Missing user ID' }, { status: 400 })
    }

    // Get target user's organization member info
    const { data: targetUserInfo, error: targetUserError } = await supabase
      .from('organization_members')
      .select('org_id, org_member_role, org_member_is_active')
      .eq('user_id', userId)
      .eq('org_member_is_active', true)
      .order('is_default_org', { ascending: false }) // Prefer default org
      .limit(1)
      .single()

    if (targetUserError) {
      console.error('Error fetching target user info:', targetUserError)
      return NextResponse.json({ error: 'Failed to find target user' }, { status: 404 })
    }

    // Get the acting user's role in the target user's organization
    const { data: actingUserRole, error: actingUserError } = await supabase
      .from('organization_members')
      .select('org_member_role')
      .eq('user_id', user.id)
      .eq('org_id', targetUserInfo.org_id)
      .single()

    if (actingUserError) {
      // Check if this is a "not found" error, which means users aren't in the same org
      if (actingUserError.code === 'PGRST116') {
        // If users aren't in the same org, check for superadmin status using centralized RBAC
        const hasAccess = await checkRbacPermission(
          { rRoles: ["superAdmin"], orgContext: "any" },
          { silentFail: true }
        )

        if (!hasAccess) {
          return NextResponse.json({
            error: 'You do not have permission to view this user\'s profile'
          }, { status: 403 })
        }
      } else {
        return NextResponse.json({ error: 'Failed to verify permissions' }, { status: 500 })
      }
    } else {
      // Check permission using the utility
      const hasPermission = canEditUserProfile(
        {
          user_id: userId,
          org_id: targetUserInfo.org_id,
          org_member_role: targetUserInfo.org_member_role,
          org_member_is_active: targetUserInfo.org_member_is_active
        } as OrganizationMemberBasic,
        actingUserRole.org_member_role,
        user.id,
        true // Same org
      )

      if (!hasPermission) {
        return NextResponse.json({
          error: 'You do not have permission to view this user\'s profile'
        }, { status: 403 })
      }
    }

    // Fetch personal information
    const { data: personalInfo, error: personalInfoError } = await supabase
      .from('user_personal_information')
      .select('*')
      .eq('id', userId)
      .single()

    if (personalInfoError) {
      if (personalInfoError.code === 'PGRST116') {
        // No personal info exists yet, return empty object
        return NextResponse.json({})
      }
      console.error('Error fetching personal info:', personalInfoError)
      return NextResponse.json({ error: 'Failed to fetch personal information' }, { status: 500 })
    }

    return NextResponse.json(personalInfo)
  } catch (error) {
    console.error('Unexpected error in GET user profile endpoint:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Extract userId from params - await it first
    const resolvedParams = await params
    const { userId } = resolvedParams
    if (!userId) {
      return NextResponse.json({ error: 'Missing user ID' }, { status: 400 })
    }

    // Get the request body
    const body = await request.json()
    if (!body || typeof body !== 'object') {
      return NextResponse.json({ error: 'Invalid request body' }, { status: 400 })
    }

    // Get target user's organization member info
    const { data: targetUserInfo, error: targetUserError } = await supabase
      .from('organization_members')
      .select('org_id, org_member_role, org_member_is_active')
      .eq('user_id', userId)
      .eq('org_member_is_active', true)
      .order('is_default_org', { ascending: false }) // Prefer default org
      .limit(1)
      .single()

    if (targetUserError) {
      console.error('Error fetching target user info:', targetUserError)
      return NextResponse.json({ error: 'Failed to find target user' }, { status: 404 })
    }

    // Get the acting user's role in the target user's organization
    const { data: actingUserRole, error: actingUserError } = await supabase
      .from('organization_members')
      .select('org_member_role')
      .eq('user_id', user.id)
      .eq('org_id', targetUserInfo.org_id)
      .single()

    if (actingUserError) {
      console.error('Error fetching acting user role:', actingUserError)
      // Check if this is a "not found" error, which means users aren't in the same org
      if (actingUserError.code === 'PGRST116') {
        // If users aren't in the same org, check for superadmin status using centralized RBAC
        const hasAccess = await checkRbacPermission(
          { rRoles: ["superAdmin"], orgContext: "any" },
          { silentFail: true }
        )

        if (!hasAccess) {
          return NextResponse.json({
            error: 'You do not have permission to update this user\'s profile'
          }, { status: 403 })
        }
      } else {
        return NextResponse.json({ error: 'Failed to verify permissions' }, { status: 500 })
      }
    } else {
      // Check permission using the utility
      const hasPermission = canEditUserProfile(
        {
          user_id: userId,
          org_id: targetUserInfo.org_id,
          org_member_role: targetUserInfo.org_member_role,
          org_member_is_active: targetUserInfo.org_member_is_active
        } as OrganizationMemberBasic,
        actingUserRole.org_member_role,
        user.id,
        true // Same org
      )

      if (!hasPermission) {
        return NextResponse.json({
          error: 'You do not have permission to update this user\'s profile'
        }, { status: 403 })
      }
    }

    // Clean up the body to include only allowed fields
    const allowedFields = [
      'first_name', 'middle_name', 'last_name', 'dob',
      'nationality', 'sex', 'height_cm',
      'passport_doc_id', 'passport_country', 'passport_date_issue',
      'passport_date_expiry'
    ]

    const cleanedData: Record<string, unknown> = {}
    allowedFields.forEach(field => {
      if (field in body) {
        cleanedData[field] = body[field]
      }
    })

    // Add updated_by field
    cleanedData.updated_by = user.id
    cleanedData.updated_at = new Date().toISOString()

    // Check if personal info record exists
    const { error: existingError } = await supabase
      .from('user_personal_information')
      .select('id')
      .eq('id', userId)
      .single()

    if (existingError && existingError.code !== 'PGRST116') { // PGRST116 = not found
      console.error('Error checking existing profile:', existingError)
      return NextResponse.json({ error: 'Failed to check existing profile' }, { status: 500 })
    }

    // Perform upsert (update or insert)
    const { data: updatedData, error: updateError } = await supabase
      .from('user_personal_information')
      .upsert({
        id: userId,
        ...cleanedData
      })
      .select()

    if (updateError) {
      console.error('Error updating profile:', updateError)
      return NextResponse.json({ error: 'Failed to update profile' }, { status: 500 })
    }

    // console.log('[API UserProfile PATCH] Update successful, data:', data)

    // Invalidate server-side auth context cache
    // invalidateServerAuthContextCache(userId) // REMOVED

    return NextResponse.json(updatedData?.[0] || cleanedData)
  } catch (error) {
    console.error('Unexpected error in update profile endpoint:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}