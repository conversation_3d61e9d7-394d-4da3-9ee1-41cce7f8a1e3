"use client";

import Link from "next/link";
import { Button } from "./ui/button";
import { BookDashed } from "lucide-react";
import { LanguageSwitcher } from "./language-switcher";
import { useTranslations } from 'next-intl';

export function FrontpageHeader() {
  const t = useTranslations('FrontpageHeader');

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-[#0A2A2A]/80 backdrop-blur-sm border-b border-teal-500/20">
      <div className="max-w-[1140px] mx-auto px-6 h-16 flex items-center justify-between">
        <Link href="/" className="flex items-center gap-2">
          <BookDashed className="w-8 h-8 text-white" />
          <span className="text-gray-200 font-medium text-2xl">
            {t('appName')}
          </span>
        </Link>

        <div className="flex items-center gap-2">
          <LanguageSwitcher />
          <Link href="/auth/login">
            <Button
              variant="ghost"
              className="text-gray-200 hover:text-white hover:bg-[#194852]/70"
            >
              {t('login')}
            </Button>
          </Link>
        </div>
      </div>
    </header>
  );
}
