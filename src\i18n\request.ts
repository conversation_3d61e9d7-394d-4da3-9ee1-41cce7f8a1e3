import { getRequestConfig } from 'next-intl/server';

export default getRequestConfig(async () => {
  // Default to English for server-side rendering
  // Client-side locale preferences are handled by LocaleProvider
  const locale = 'en';

  try {
    const messages = (await import(`../../messages/${locale}.json`)).default;
    return {
      locale,
      messages
    };
  } catch (error) {
    console.error(`[i18n/request] Failed to load messages for locale ${locale}:`, error);
    // Return empty messages as fallback
    return {
      locale,
      messages: {}
    };
  }
});
