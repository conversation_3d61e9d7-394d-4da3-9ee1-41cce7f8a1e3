"use client";

import { useRef, useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { ChevronRight } from "lucide-react";
import { useToastMessages } from "@/hooks/use-toast-messages";
import {
  Toolt<PERSON>,
  Toolt<PERSON><PERSON>ontent,
  Too<PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import type { UserPersonalInfo } from "@/types/user"; // Use new central type
import type { ProfileFormProps } from "@/types/components/profile/ProfileFormProps";

const formSections = [
  {
    title: "Personal Information",
    fields: ["first_name", "middle_name", "last_name", "dob", "sex"] as const,
  },
  {
    title: "Identity Details",
    fields: [
      "passport_name",
      "birth_name",
      "previous_name",
      "nickname",
      "non_latin_full_name",
    ] as const,
  },
  {
    title: "Nationality & Birth",
    fields: ["nationality", "nationality_birth", "place_of_birth"] as const,
  },
  {
    title: "Passport Details",
    fields: [
      "passport_doc_id",
      "passport_date_issue",
      "passport_date_expiry",
      "passport_country",
    ] as const,
  },
  {
    title: "Physical Attributes",
    fields: ["height_cm"] as const,
  },
] as const;

const getInputType = (field: string) => {
  if (field === "dob" || field.includes("date")) return "date";
  if (field === "height_cm") return "number";
  return "text";
};

const getInputProps = (field: string) => {
  if (field === "height_cm") {
    return {
      min: 0,
      max: 300,
      step: 1,
    };
  }
  return {};
};

export function ProfileForm({
  profile,
  personalInfo,
  updateProfile,
}: ProfileFormProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isButtonVisible, setIsButtonVisible] = useState(true);
  const observerRef = useRef<HTMLDivElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const toastMessages = useToastMessages();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsButtonVisible(entry.isIntersecting);
      },
      {
        threshold: 0,
        rootMargin: "0px 0px 100px 0px",
      }
    );

    if (observerRef.current) {
      observer.observe(observerRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const formData = new FormData(e.currentTarget);
      await updateProfile(formData);
      toastMessages.user.updateSuccess("Profile");
    } catch (error) {
      toastMessages.user.updateError(
        error instanceof Error ? error.message : undefined
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="p-6 pt-0">
      <div className="mb-6 space-y-4">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator>
              <ChevronRight className="h-4 w-4" />
            </BreadcrumbSeparator>
            <BreadcrumbItem>
              <BreadcrumbPage>Profile</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-[#194852]">Profile</h2>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow dark:bg-gray-800 p-6">
        <form
          ref={formRef}
          onSubmit={handleSubmit}
          className="space-y-4 p-4 max-w-7xl mx-auto relative"
        >
          <div
            className="flex items-center space-x-4 pb-4 border-b"
            ref={observerRef}
          >
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Avatar
                    onClick={handleAvatarClick}
                    className="cursor-pointer w-20 h-20 rounded-md relative before:absolute before:-inset-[4px] before:rounded-lg before:bg-gradient-to-r before:from-[#ff0000] before:via-[#00ff00] before:to-[#0000ff] before:animate-[gradient_2s_linear_infinite] before:-z-10 before:bg-[length:300%_300%] before:opacity-100"
                  >
                    <AvatarImage
                      src={profile?.avatar_url || undefined}
                      alt="User avatar"
                      className="rounded-md"
                    />
                    <AvatarFallback className="rounded-md">
                      {profile?.full_name?.[0]?.toUpperCase() || "U"}
                    </AvatarFallback>
                  </Avatar>
                </TooltipTrigger>
                <TooltipContent className="bg-[#0A2C35] text-white border-[#0A2C35]">
                  <p>Click image to Change avatar</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <input
              type="file"
              name="avatar"
              accept="image/*"
              ref={fileInputRef}
              className="hidden"
            />
            <div className="flex justify-between w-full">
              <div className="">
                <h2 className="text-lg text-[#194852]">
                  Hi {profile?.full_name || "there"}, welcome back
                </h2>
                <p className="text-sm text-gray-500">
                  Manage your personal information
                </p>
              </div>
              <Button
                className="bg-[#194852] hover:bg-[#194852]/90 w-full sm:w-auto"
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Updating..." : "Update Profile"}
              </Button>
            </div>
          </div>

          {/* Sliding Header Section */}
          <div
            className={`fixed transition-all duration-300 ease-in-out transform inline-block ${
              !isButtonVisible
                ? "translate-y-0 opacity-100"
                : "translate-y-full opacity-0"
            } bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 py-4 shadow-lg z-50`}
            style={{
              width: "40%",
              left: "50%",
              transform: `translateX(-50%) ${
                !isButtonVisible ? "translateY(0)" : "translateY(100%)"
              }`,
              bottom: "20px",
              borderRadius: "8px",
              maxWidth: "calc(100% - 320px)",
              marginLeft: "32px",
            }}
          >
            <div className="px-4">
              <div className="flex items-center justify-between space-x-4">
                <div className="flex items-center space-x-4">
                  <Avatar className="w-10 h-10 rounded-md relative before:absolute before:-inset-[4px] before:rounded-lg before:bg-gradient-to-r before:from-[#ff0000] before:via-[#00ff00] before:to-[#0000ff] before:animate-[gradient_2s_linear_infinite] before:-z-10 before:bg-[length:300%_300%] before:opacity-100">
                    <AvatarImage
                      src={profile?.avatar_url || undefined}
                      alt="User avatar"
                      className="rounded-md"
                    />
                    <AvatarFallback className="rounded-md">
                      {profile?.full_name?.[0]?.toUpperCase() || "U"}
                    </AvatarFallback>
                  </Avatar>
                  <div className="hidden sm:block">
                    <h2 className="text-sm font-medium text-[#194852]">
                      Hi {profile?.full_name || "there"}
                    </h2>
                    <p className="text-xs text-gray-500">
                      Manage your personal information
                    </p>
                  </div>
                </div>
                <Button
                  className="bg-[#194852] hover:bg-[#194852]/90 w-full sm:w-auto"
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Updating..." : "Update Profile"}
                </Button>
              </div>
            </div>
          </div>

          <Accordion
            type="single"
            className="pb-6"
            collapsible
            defaultValue="Personal Information"
          >
            {formSections.map((section) => (
              <AccordionItem key={section.title} value={section.title}>
                <AccordionTrigger className="text-md text-[#194852]">
                  {section.title}
                </AccordionTrigger>
                <AccordionContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                    {section.fields.map((field) => (
                      <div key={field} className="space-y-2">
                        <Label htmlFor={field} className="capitalize">
                          {field.replace(/_/g, " ")}
                        </Label>
                        <Input
                          id={field}
                          name={field}
                          type={getInputType(field)}
                          defaultValue={
                            personalInfo?.[field as keyof UserPersonalInfo] || ""
                          }
                          className="w-full"
                          {...getInputProps(field)}
                        />
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </form>
        <p className="text-gray-600 max-w-7xl mx-auto">
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Aperiam
          voluptatibus sed recusandae molestias harum, cupiditate ullam facere
          nobis! Non illum unde nisi est eos consequatur nulla quae corrupti?
          Rem atque quo, deleniti minima illum tenetur nihil hic aliquid non
          iure amet sunt, laborum, natus eos perspiciatis officiis quos beatae
          soluta est voluptas ea tempore suscipit deserunt nulla. Voluptatum
          maxime quo ut veniam reprehenderit vel unde non adipisci, dolores quam
          itaque assumenda ipsum id corporis sed cupiditate tenetur nemo. Illum
          tempore impedit, et quos, autem, perspiciatis culpa in optio facere ad
          quis officiis eius omnis sit doloremque asperiores. Cupiditate
          perferendis, consequuntur est ipsa placeat sequi. Fugit quas nesciunt
          ipsa blanditiis numquam aspernatur, tempora, esse aperiam vitae dolor
          ad a. Cupiditate dolore officiis sit ullam, ex tempore id. Rerum vel
          excepturi autem optio quia esse, accusantium aut pariatur fugit veniam
          corporis earum debitis dolorum placeat, libero iste porro facilis
          iusto ea ipsam deserunt! Temporibus explicabo odio incidunt ex
          suscipit distinctio, fugiat minima odit molestias hic. Fuga voluptate
          suscipit fugiat unde impedit veritatis nulla dicta natus commodi,
          laborum sunt voluptatibus illum, cupiditate, atque rem iusto vero.
          Aliquid, reprehenderit a numquam sunt doloribus ex aspernatur tempore
          dignissimos nobis dolorum quo fugiat provident eaque quia nesciunt
          vero pariatur quidem error fugit vel ipsa ullam! Quasi est dolore
          minus in ducimus magni. Maiores pariatur quas sint, repellendus magni,
          ut hic sunt nam at iure explicabo! Neque placeat unde dolores
          laudantium, doloremque saepe adipisci sed? Numquam, amet! Odit, eos?
          Ex eaque saepe ea unde pariatur quas cumque ad velit itaque modi
          libero doloremque magni in corrupti, cupiditate ducimus aliquam
          assumenda alias, corporis, voluptatibus ipsum ullam! Quam nostrum in
          omnis beatae ducimus sapiente suscipit id quidem quos, exercitationem
          consequuntur facilis vero corporis ex? Maxime illo, dolores aut
          reiciendis ratione illum temporibus dolor voluptatem placeat officiis
          natus eaque voluptatibus ex id cum minus? Cum molestias eius,
          architecto debitis fugiat voluptates, quasi eaque unde asperiores
          voluptatum illo maiores minima iure eos dicta aperiam fuga
          necessitatibus porro similique facilis harum numquam laborum dolorem.
          Aspernatur fuga laborum alias assumenda recusandae magnam reiciendis,
          necessitatibus illum officiis culpa hic vero ad consequuntur impedit
          dolorem repellendus. Fuga pariatur eum, temporibus error possimus
          nulla? Ratione pariatur tempora nam possimus quisquam dolorum labore
          cupiditate, illo corrupti aut debitis vero veniam nostrum est quos
          magnam qui nemo eaque voluptatum cum ipsa minima eveniet! Libero optio
          fugit totam enim facilis molestiae amet ipsa rerum porro consequuntur,
          ipsum odio quidem obcaecati eveniet magnam, adipisci sed deleniti
          nobis ut culpa iure aperiam modi similique ullam? Expedita molestias
          ipsum enim itaque totam praesentium consequatur possimus pariatur
          debitis adipisci! Voluptas veritatis delectus quis vitae, possimus ea,
          eaque architecto amet dolorem culpa mollitia quae animi. Quidem enim
          asperiores eligendi alias mollitia minima unde ipsa! Corrupti
          perferendis a voluptates quibusdam, eius adipisci ipsam doloribus
          voluptate at autem ex consequuntur. Reiciendis aliquam veniam
          doloribus neque repellendus magni veritatis cupiditate ex saepe vero
          corrupti repellat distinctio aut illum, maxime voluptate voluptas
          sapiente tempore aliquid cum. Voluptatem, sapiente consectetur quos,
          fugit id possimus consequuntur illum deleniti doloremque veritatis
          quia reiciendis accusantium temporibus voluptates, voluptas obcaecati
          aut iste enim deserunt laudantium atque. Nobis eum animi architecto
          iste. Perferendis corrupti sapiente accusamus earum fugiat error ad,
          possimus adipisci enim voluptatem sunt architecto consequatur ea totam
          repudiandae, unde quia hic blanditiis dolore alias assumenda.
          Distinctio, officiis incidunt aperiam iusto quis, hic harum ut
          voluptatibus est, labore dolore quaerat sit et itaque asperiores
          inventore cum. Sequi sapiente incidunt a obcaecati alias aperiam
          commodi omnis eveniet impedit animi tempora mollitia perferendis
          tenetur dolorem fuga est, deleniti ratione labore accusantium rem
          soluta. Perferendis, maiores est ducimus id recusandae aut saepe
          voluptate architecto exercitationem ullam, dolorum quod esse
          voluptates, ea aperiam animi. Debitis laborum accusamus ea consectetur
          pariatur voluptate minima deserunt eum itaque quas, fuga aspernatur
          architecto, amet delectus! Quia, natus! At veritatis ab aliquam iure
          cum! Quisquam aliquid inventore repudiandae cumque eaque quasi. Itaque
          ad unde tempore vel quisquam nulla omnis, aspernatur recusandae
          aperiam eligendi ullam dolores quod beatae quibusdam possimus corrupti
          perferendis! Temporibus beatae saepe cum architecto iste, error,
          repellendus earum modi vitae ullam iusto obcaecati ut nemo numquam
          voluptatem delectus. Dolores laudantium in voluptatem nisi
          repudiandae, error sit rerum non eligendi ab id debitis officia
          minima! Accusamus facere, doloribus ex itaque atque at cumque
          provident, velit voluptatum mollitia adipisci blanditiis, a quisquam.
          Maiores corrupti, quisquam recusandae hic natus error iusto ipsa,
          excepturi eligendi eveniet consequuntur quam dolorem id repellat
          impedit perspiciatis sequi officia omnis fugit voluptates corporis
          similique? Alias, officiis. Nisi reprehenderit deleniti commodi,
          natus, doloribus fuga animi vel rerum distinctio eos eveniet ut, earum
          unde quae veritatis suscipit reiciendis. Esse, ut doloribus vel
          debitis illum ipsam, dolore praesentium sunt exercitationem voluptate
          eum ducimus nam dolor nihil nemo. Voluptatum recusandae quia ipsa
          deleniti dolorum iusto? Facilis laboriosam iure recusandae dolores
          esse accusantium corporis voluptatem saepe officiis, consectetur quas
          quod eius doloribus nisi. Error, architecto. Molestias quasi minus
          tenetur vitae. Accusantium, quisquam, sunt sint nulla blanditiis
          quidem asperiores odit dolorem molestias, necessitatibus mollitia
          optio. Veritatis unde consequatur quo asperiores animi inventore iure
          dolores, dicta illo id exercitationem assumenda at reprehenderit
          pariatur placeat esse praesentium adipisci. Quos, unde autem! Aliquam
          odio numquam enim dignissimos est quis atque pariatur ipsum nulla
          quaerat architecto aspernatur illum, sint eaque porro delectus
          veritatis suscipit dolore fuga? Iusto autem distinctio odio. Animi
          itaque quidem sapiente quo beatae odit, labore atque aliquid quas et.
          Voluptates sapiente quo modi quibusdam corrupti a velit, harum nemo
          quidem architecto! Tempore amet neque exercitationem blanditiis sed
          assumenda optio, tenetur incidunt repellendus repudiandae ex? Porro
          perspiciatis hic deleniti. Magnam doloremque tempore ad, enim autem
          modi culpa quas, cum error pariatur quidem sint. Eos mollitia
          repudiandae alias quia quidem optio temporibus. Quam esse
          reprehenderit distinctio repellendus nostrum veniam, deserunt, eos
          laboriosam necessitatibus eveniet illo voluptate dolore nulla harum
          non? Iusto nam hic dolores deleniti fugit voluptas minus commodi
          laborum et, nulla nihil temporibus maxime, aliquam consectetur dolor
          perferendis quae excepturi dolore eveniet ex. Recusandae sint
          cupiditate, culpa temporibus nemo omnis, explicabo repellendus beatae
          delectus commodi esse, quam laborum maxime. Quae atque veritatis
          exercitationem distinctio, blanditiis maxime?
        </p>
      </div>
    </div>
  );
}
