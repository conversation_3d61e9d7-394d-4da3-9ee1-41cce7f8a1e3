"use client";

import { toast } from "sonner";

// Types for toast parameters
export type ToastOptions = {
  description?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
};

// Type for translation function
type TranslationFunction = (key: string, values?: Record<string, any>) => string;

/**
 * Centralized toast message system with internationalization support
 *
 * Usage:
 * import { createToastMessages } from "@/lib/toast-messages";
 * import { useTranslations } from 'next-intl';
 *
 * // In your component:
 * const t = useTranslations('Toast');
 * const toastMessages = createToastMessages(t);
 *
 * // Success message
 * toastMessages.auth.loginSuccess();
 *
 * // Error with custom message
 * toastMessages.organization.createError("Custom error message");
 *
 * // With options
 * toastMessages.organization.switchSuccess({
 *   duration: 5000,
 *   action: {
 *     label: "Undo",
 *     onClick: () => handleUndo()
 *   }
 * });
 */

/**
 * Factory function to create toast messages with translation support
 */
export function createToastMessages(t: TranslationFunction) {
  // Generic toast function helpers
  const createToast = {
    success: (message: string, options?: ToastOptions) => {
      toast.success(message, options);
    },
    error: (message: string, options?: ToastOptions) => {
      toast.error(message, options);
    },
    info: (message: string, options?: ToastOptions) => {
      toast.info(message, options);
    },
    warning: (message: string, options?: ToastOptions) => {
      toast.warning(message, options);
    },
  };

  // Organization related toast messages
  const organization = {
    createSuccess: (options?: ToastOptions) => {
      createToast.success(t("organization.createSuccess"), options);
    },
    createError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("organization.createError"), options);
    },
    deleteSuccess: (options?: ToastOptions) => {
      createToast.success(t("organization.deleteSuccess"), options);
    },
    deleteError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("organization.deleteError"), options);
    },
    updateSuccess: (options?: ToastOptions) => {
      createToast.success(t("organization.updateSuccess"), options);
    },
    updateError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("organization.updateError"), options);
    },
    switchSuccess: (options?: ToastOptions) => {
      createToast.success(t("organization.switchSuccess"), options);
    },
    switchError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("organization.switchError"), options);
    },
    deletionCancelled: (options?: ToastOptions) => {
      createToast.info(t("organization.deletionCancelled"), options);
    },
    removedFrom: (orgName: string = "the organization", options?: ToastOptions) => {
      createToast.info(t("organization.removedFrom", { orgName }), options);
    },
  };

  // Authentication related toast messages
  const auth = {
    loginSuccess: (options?: ToastOptions) => {
      createToast.success(t("auth.loginSuccess"), options);
    },
    loginError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("auth.loginError"), options);
    },
    signupSuccess: (options?: ToastOptions) => {
      createToast.success(t("auth.signupSuccess"), options);
    },
    signupError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("auth.signupError"), options);
    },
    logoutSuccess: (options?: ToastOptions) => {
      createToast.success(t("auth.logoutSuccess"), options);
    },
    logoutError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("auth.logoutError"), options);
    },
    sessionError: (options?: ToastOptions) => {
      createToast.error(t("auth.sessionError"), options);
    },
    magicLinkExpired: (options?: ToastOptions) => {
      createToast.error(t("auth.magicLinkExpired"), options);
    },
    magicLinkInvalid: (options?: ToastOptions) => {
      createToast.error(t("auth.magicLinkInvalid"), options);
    },
    magicLinkSent: (options?: ToastOptions) => {
      createToast.success(t("auth.magicLinkSent"), options);
    },
    authenticationFailed: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("auth.authenticationFailed"), options);
    },
  };

  // Invite related toast messages
  const invite = {
    createSuccess: (options?: ToastOptions) => {
      createToast.success(t("invite.createSuccess"), options);
    },
    createError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("invite.createError"), options);
    },
    deactivateSuccess: (options?: ToastOptions) => {
      createToast.success(t("invite.deactivateSuccess"), options);
    },
    deactivateError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("invite.deactivateError"), options);
    },
    copySuccess: (options?: ToastOptions) => {
      createToast.success(t("invite.copySuccess"), options);
    },
    joinSuccess: (message?: string, options?: ToastOptions) => {
      createToast.success(message || t("invite.joinSuccess"), options);
    },
    joinError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("invite.joinError"), options);
    },
    missingCode: (options?: ToastOptions) => {
      createToast.error(t("invite.missingCode"), options);
    },
  };

  // User related toast messages
  const user = {
    updateSuccess: (field: string = "Profile", options?: ToastOptions) => {
      createToast.success(t("user.updateSuccess", { field }), options);
    },
    updateError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("user.updateError"), options);
    },
    removeSuccess: (name: string = "Member", options?: ToastOptions) => {
      createToast.info(t("user.removeSuccess", { name }), options);
    },
    removeError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("user.removeError"), options);
    },
    fetchError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("user.fetchError"), options);
    },
    roleUpdated: (options?: ToastOptions) => {
      createToast.info(t("user.roleUpdated"), options);
    },
    statusUpdate: (name: string, status: string) => {
      toast.info(t("user.statusUpdate", { name, status }));
    },
    roleUpdate: (name: string, role: string, orgName: string) => {
      toast.info(t("user.roleUpdate", { name, role, orgName }));
    },
    added: (name: string, orgName: string) => {
      toast.success(t("user.added", { name, orgName }));
    },
    removed: (name: string, orgName: string) => {
      toast.success(t("user.removed", { name, orgName }));
    },
  };

  // Form/flow related toast messages
  const form = {
    saveSuccess: (options?: ToastOptions) => {
      createToast.success(t("form.saveSuccess"), options);
    },
    saveError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("form.saveError"), options);
    },
    createSuccess: (options?: ToastOptions) => {
      createToast.success(t("form.createSuccess"), options);
    },
    createError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("form.createError"), options);
    },
    deleteSuccess: (options?: ToastOptions) => {
      createToast.success(t("form.deleteSuccess"), options);
    },
    deleteError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("form.deleteError"), options);
    },
    fetchError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("form.fetchError"), options);
    },
    reorderSuccess: (options?: ToastOptions) => {
      createToast.success(t("form.reorderSuccess"), options);
    },
    reorderError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("form.reorderError"), options);
    },
  };

  // Generic common messages
  const common = {
    unexpectedError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("common.unexpectedError"), options);
    },
    accessDenied: (options?: ToastOptions) => {
      createToast.error(t("common.accessDenied"), options);
    },
    copySuccess: (item: string = "Text", options?: ToastOptions) => {
      createToast.success(t("common.copySuccess", { item }), options);
    },
    uploadSuccess: (options?: ToastOptions) => {
      createToast.success(t("common.uploadSuccess"), options);
    },
    uploadError: (message?: string, options?: ToastOptions) => {
      createToast.error(message || t("common.uploadError"), options);
    },
    loadError: (resource: string = "data", options?: ToastOptions) => {
      createToast.error(t("common.loadError", { resource }), options);
    },
  };

  // Return all toast categories
  return {
    organization,
    auth,
    invite,
    user,
    form,
    common,

    // Direct access to the basic toast functions for custom cases
    success: createToast.success,
    error: createToast.error,
    info: createToast.info,
    warning: createToast.warning,
  };
}