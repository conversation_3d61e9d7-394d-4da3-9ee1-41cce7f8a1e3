import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label as LabelUI } from "@/components/ui/label";
import { useState, useEffect } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label, LabelType, LABEL_TYPES } from "@/types/components/labels/Label";
import { LabelEditDialogProps } from "@/types/components/labels/LabelEditDialogProps";
import { useTranslations } from 'next-intl';

export function LabelEditDialog({
  label,
  isOpen,
  onClose,
  onSave,
}: LabelEditDialogProps) {
  const t = useTranslations('LabelEditDialog');
  const [formData, setFormData] = useState<Partial<Label>>({
    name: "",
    description: "",
    type: "text",
  });

  useEffect(() => {
    if (label) {
      setFormData(label);
    } else {
      setFormData({ name: "", description: "", type: "text" });
    }
  }, [label]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('title')}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <LabelUI htmlFor="name">{t('name')}</LabelUI>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, name: e.target.value }))
              }
              required
            />
          </div>
          <div className="space-y-2">
            <LabelUI htmlFor="description">{t('description')}</LabelUI>
            <Input
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, description: e.target.value }))
              }
            />
          </div>
          <div className="space-y-2">
            <LabelUI htmlFor="type">{t('type')}</LabelUI>
            <Select
              value={formData.type || 'text'}
              onValueChange={(value: LabelType) =>
                setFormData((prev) => ({ ...prev, type: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder={t('selectType')} />
              </SelectTrigger>
              <SelectContent>
                {LABEL_TYPES.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              {t('cancel')}
            </Button>
            <Button type="submit">{t('save')}</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
} 