import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import { OrganizationCheckProvider } from "@/providers/organization-check-provider";
import { GlobalDashboardProvider } from "@/components/providers/global-dashboard-provider";
import { SWRProvider } from "@/components/providers/swr-provider";
import { LocaleProvider } from "@/components/providers/locale-provider";
import { loadMessages } from '@/lib/i18n/locale-utils';

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Next.js Supabase SSR",
  description: "Next.js application with Supabase SSR authentication",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Load initial locale and messages for server-side rendering
  const initialLocale = 'en'; // Default to English for SSR
  const initialMessages = await loadMessages(initialLocale);

  return (
    <html lang={initialLocale}>
      <body className={inter.className}>
        <SWRProvider>
          <LocaleProvider
            initialMessages={initialMessages}
            initialLocale={initialLocale}
          >
            <GlobalDashboardProvider>
              <OrganizationCheckProvider>
                {children}
                <Toaster />
              </OrganizationCheckProvider>
            </GlobalDashboardProvider>
          </LocaleProvider>
        </SWRProvider>
      </body>
    </html>
  );
}
