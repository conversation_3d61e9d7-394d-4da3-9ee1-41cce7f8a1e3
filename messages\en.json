{"Common": {"buttons": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "submit": "Submit", "loading": "Loading...", "confirm": "Confirm", "create": "Create", "update": "Update", "remove": "Remove", "search": "Search", "filter": "Filter", "close": "Close", "open": "Open"}, "actions": {"create": "Create", "update": "Update", "remove": "Remove", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import"}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "disabled": "Disabled", "enabled": "Enabled", "loading": "Loading", "success": "Success", "error": "Error"}, "navigation": {"next": "Next", "previous": "Previous", "home": "Home", "back": "Back", "forward": "Forward"}}, "Tables": {"headers": {"name": "Name", "email": "Email", "role": "Role", "status": "Status", "actions": "Actions", "createdAt": "Created", "updatedAt": "Updated", "id": "ID"}, "pagination": {"showing": "Showing {start} to {end} of {total} results", "rowsPerPage": "Rows per page", "noData": "No data available", "page": "Page", "of": "of"}}, "Forms": {"validation": {"required": "This field is required", "email": "Please enter a valid email", "minLength": "Must be at least {min} characters", "maxLength": "Must be no more than {max} characters", "passwordMismatch": "Passwords do not match", "invalidFormat": "Invalid format"}, "labels": {"firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "name": "Name", "description": "Description"}, "placeholders": {"email": "<EMAIL>", "search": "Search...", "enterText": "Enter text..."}}, "Errors": {"general": "An error occurred", "network": "Network error occurred", "unauthorized": "You are not authorized to perform this action", "notFound": "The requested resource was not found", "serverError": "Internal server error", "validationFailed": "Validation failed"}, "Language": {"switchLanguage": "Switch Language", "currentLanguage": "Current Language", "english": "English", "dutch": "Nederlands", "thai": "ไทย"}, "Sidebar": {"appName": "Agency", "appNameLight": "Forms", "sections": {"main": "Main", "admin": "Admin", "developer": "Developer"}}, "Navigation": {"sections": {"main": "Main", "admin": "Admin", "developer": "Developer"}, "main": {"dashboard": "Dashboard", "myForms": "My Forms", "createForm": "Create Form", "forms": "Forms", "handbooks": "Handbooks", "clients": "Clients", "clientInvites": "Client Invites", "settings": "Settings"}, "admin": {"organizationSettings": "Organization Settings", "members": "Members", "invites": "<PERSON><PERSON><PERSON>", "memberInvites": "Member Invites", "labels": "Labels", "flows": "Flows", "billing": "Billing"}, "developer": {"organizations": "Organizations", "allUsers": "All Users", "userInvites": "User Invites", "createOrganization": "Create Organization", "testEventSystem": "Test Event System", "rbacTest": "RBAC Test"}}, "TopBar": {"organizationDisabled": "{orgName} is disabled", "organizationDisabledGeneric": "Organization is disabled"}, "Toast": {"organization": {"createSuccess": "Organization created successfully", "createError": "Failed to create organization", "deleteSuccess": "Organization deleted successfully", "deleteError": "Failed to delete organization", "updateSuccess": "Organization updated successfully", "updateError": "Failed to update organization", "switchSuccess": "Organization switched successfully", "switchError": "Failed to switch organization", "deletionCancelled": "Deletion cancelled", "removedFrom": "You have been removed from {orgName}"}, "auth": {"loginSuccess": "Logged in successfully", "loginError": "<PERSON><PERSON> failed", "signupSuccess": "Account created successfully", "signupError": "Signup failed", "logoutSuccess": "Logged out successfully", "logoutError": "Logout failed", "sessionError": "Session error, please log in again", "magicLinkExpired": "Your login link has expired. Please request a new one.", "magicLinkInvalid": "This login link is invalid or has already been used.", "magicLinkSent": "Login link sent! Check your email.", "authenticationFailed": "Authentication failed"}, "invite": {"createSuccess": "Invite created successfully", "createError": "Failed to create invite", "deactivateSuccess": "Invite code deactivated successfully", "deactivateError": "Failed to deactivate invite code", "copySuccess": "Invite code copied to clipboard", "joinSuccess": "Successfully joined organization", "joinError": "Failed to join organization", "missingCode": "Please enter an invite code"}, "user": {"updateSuccess": "{field} updated successfully", "updateError": "Failed to update profile", "removeSuccess": "Member removed: {name}", "removeError": "Failed to remove member", "fetchError": "Failed to fetch user data", "roleUpdated": "User role updated", "statusUpdate": "{name}'s status updated to {status}", "roleUpdate": "{name}'s role updated to {role} in organization {orgName}", "added": "New member added: {name} to organization {orgName}", "removed": "Successfully removed {name} from organization {orgName}"}, "form": {"saveSuccess": "Form saved successfully", "saveError": "Failed to save form", "createSuccess": "Form created successfully", "createError": "Failed to create form", "deleteSuccess": "Form deleted successfully", "deleteError": "Failed to delete form", "fetchError": "Failed to fetch forms", "reorderSuccess": "Items reordered successfully", "reorderError": "Failed to reorder items"}, "common": {"unexpectedError": "An unexpected error occurred", "accessDenied": "Access denied", "copySuccess": "{item} copied to clipboard", "uploadSuccess": "File uploaded successfully", "uploadError": "Failed to upload file", "loadError": "Failed to load {resource}"}}, "Pages": {"dashboard": {"title": "Dashboard", "breadcrumb": "Dashboard"}, "accountDisabled": {"title": "Account Disabled", "messageWithOrg": "Your account in the organization \"{orgName}\" has been disabled. Please contact an administrator from this organization to reactivate your account, or switch to another organization if you have access to multiple organizations.", "messageGeneric": "Your account has been disabled in the current organization. Please contact an administrator to reactivate your account or switch to another organization.", "switchOrganization": "Switch Organization", "signOut": "Sign Out"}, "organizationDisabled": {"title": "Organization Disabled", "messageWithOrg": "The organization \"{orgName}\" has been disabled. Please use the organization switcher in the sidebar to switch to an active organization or contact AgencyForms to reactivate this organization.", "messageGeneric": "Your current organization has been disabled. Please use the organization switcher in the sidebar to switch to an active organization or contact AgencyForms for assistance.", "signOut": "Sign Out"}}, "ServerActions": {"invitation": {"insufficientPermissions": "Insufficient permissions", "authenticationRequired": "Authentication required", "membersCanOnlyInviteClients": "Organization members can only invite clients", "cannotAssignRolesAboveAdmin": "Cannot assign roles above orgAdmin level", "cannotAssignSuperAdminRoles": "Cannot assign superAdmin or supportAdmin roles via invitations", "pendingInviteExists": "There is already a pending invite for {email}", "userAlreadyMember": "User with email {email} is already a member of this organization", "failedToCreateInvitation": "Failed to create invitation", "failedToSendEmail": "Failed to send email. Please try again.", "invalidInputData": "Invalid input data", "internalServerError": "Internal server error"}, "organization": {"notAuthenticated": "Not authenticated", "failedToSwitch": "Failed to switch organization: {message}", "unknownError": "Unknown error"}, "validation": {"emailRequired": "Email is required", "emailTooLong": "Email is too long", "invalidEmail": "Please enter a valid email address", "invalidRequest": "Invalid request", "invalidEmailFormat": "Invalid email format"}}, "Auth": {"form": {"loginWithEmail": "Login with email", "enterEmailLabel": "Enter your email to receive a login link", "emailPlaceholder": "<EMAIL>", "sendMagicLink": "Send magic link", "sendingMagicLink": "Sending magic link...", "waitSeconds": "Wait {seconds}s", "completeSecurityFirst": "Complete security verification first", "continueWithGoogle": "Continue with Google", "or": "Or", "captchaLabel": "CAPTCHA verification by Cloudflare", "securityVerificationFailed": "Security verification failed. Please try again.", "retryingSecurityVerification": "Retrying security verification...", "securityVerificationExpired": "Security verification expired. It will refresh automatically.", "checkYourEmail": "Check your email", "emailSentMessage": "If you are an existing user you'll receive a login link shortly. Please also check your junk/spam folder before trying again.", "sendAnotherLink": "Send another link", "invalidEmailFormat": "Invalid email format", "networkError": "Network error. Please check your connection and try again.", "failedToSendMagicLink": "Failed to send magic link", "failedToSignInWithGoogle": "Failed to sign in with Google", "pleaseWaitBeforeRequesting": "Please wait {seconds} seconds before requesting another magic link."}}}