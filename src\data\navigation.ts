import { NavigationSection } from '../types/navigation';

// Local interface definitions for NavigationItem and NavigationSection are removed.
// The data below will be typed by the imported NavigationSection.

// Role IDs from the database:
// 1: Superadmin (most permissive)
// 2: Supportadmin
// 3: Orgadmin
// 4: Orgmember
// 5: Orgaccounting
// 6: Orgclient (most restricted)

export const navigationData: NavigationSection[] = [
  {
    titleKey: 'sections.main',
    items: [
      { labelKey: 'main.dashboard', icon: 'layoutDashboard', href: '/dashboard' },
      { labelKey: 'main.myForms', icon: 'shoppingBag', href: '/dashboard/my-forms' },
      {
        labelKey: 'main.createForm',
        icon: 'plus',
        href: '/dashboard/create-form',
        RbacConditions: { rMinRole: 'orgMember' }
      },
      {
        labelKey: 'main.forms',
        icon: 'formInput',
        href: '/dashboard/forms',
        RbacConditions: { rMinRole: 'orgClient' }
      },
      {
        labelKey: 'main.handbooks',
        icon: 'book',
        href: '/dashboard/handbooks',
        RbacConditions: { rMinRole: 'orgClient' }
      },
      {
        labelKey: 'main.clients',
        icon: 'users',
        href: '/dashboard/clients',
        RbacConditions: { rMinRole: 'orgMember' }
      },
      {
        labelKey: 'main.clientInvites',
        icon: 'userPlus',
        href: '/dashboard/clients/invites',
        RbacConditions: { rMinRole: 'orgMember' }
      },
      {
        labelKey: 'main.settings',
        icon: 'settings',
        href: '/dashboard/settings',
        RbacConditions: { rMinRole: 'orgAdmin' }
      },
    ],
  },
  {
    titleKey: 'sections.admin',
    RbacConditions: { rMinRole: 'orgAdmin' },
    items: [
      {
        labelKey: 'admin.organizationSettings',
        icon: 'settings',
        href: '/dashboard/admin/organization-settings',
      },
      {
        labelKey: 'admin.members',
        icon: 'users',
        href: '/dashboard/admin/members',
      },
      {
        labelKey: 'admin.invites',
        icon: 'userPlus',
        href: '/dashboard/admin/invites',
      },
      {
        labelKey: 'admin.memberInvites',
        icon: 'mail',
        href: '/dashboard/admin/members/invites',
      },
      {
        labelKey: 'admin.labels',
        icon: 'library',
        href: '/dashboard/admin/labels',
        RbacConditions: { rMinRole: 'orgAdmin' }
      },
      {
        labelKey: 'admin.flows',
        icon: 'Workflow',
        href: '/dashboard/admin/flows',
        RbacConditions: { rMinRole: 'orgAdmin' }
      },
      {
        labelKey: 'admin.billing',
        icon: 'CircleDollarSign',
        href: '/dashboard/profile/billing',
        RbacConditions: { rMinRole: 'orgAdmin' }
      },
    ],
  },
  {
    titleKey: 'sections.developer',
    RbacConditions: { rRoles: ['superAdmin', 'supportAdmin'] },
    items: [
      {
        labelKey: 'developer.organizations',
        icon: 'building',
        href: '/dashboard/developer/organizations'
      },
      {
        labelKey: 'developer.allUsers',
        icon: 'Users',
        href: '/dashboard/developer/users'
      },
      {
        labelKey: 'developer.userInvites',
        icon: 'mail',
        href: '/dashboard/developer/users/invites'
      },
      {
        labelKey: 'developer.createOrganization',
        icon: 'PlusCircle',
        href: '/dashboard/developer/create-organization',
      },
      {
        labelKey: 'developer.testEventSystem',
        icon: 'TestTube2',
        href: '/dashboard/developer/test-event'
      },
      {
        labelKey: 'developer.rbacTest',
        icon: 'shield',
        href: '/dashboard/rbac-test'
      }
    ],
  },
];