'use server'

// import { revalidatePath } from 'next/cache'
import { createClient } from '@/lib/supabase/server'
import { getServerActionTranslations } from '@/lib/i18n/server-translations'

export async function switchOrganization(orgId: string) {
  // Get translations for error messages
  const tOrganization = await getServerActionTranslations('organization');

  try {
    console.log('Server action: Switching organization to:', orgId);

    const supabase = await createClient()

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      console.error('Server action: Not authenticated', userError);
      throw new Error(tOrganization('notAuthenticated'))
    }
    
    // Use the DB function to set the context
    const { error } = await supabase.rpc('set_user_organization_context', {
      p_user_id: user.id,
      p_org_id: orgId
    })
    
    if (error) {
      console.error('Server action: Error switching organization:', error)
      throw new Error(tOrganization('failedToSwitch', { message: error.message }))
    }
    
    console.log('Server action: Successfully switched organization to', orgId);
    
    return { success: true }
  } catch (err) {
    console.error('Server action: Error in switchOrganization:', err)
    return {
      success: false,
      error: err instanceof Error ? err.message : tOrganization('unknownError')
    }
  }
} 