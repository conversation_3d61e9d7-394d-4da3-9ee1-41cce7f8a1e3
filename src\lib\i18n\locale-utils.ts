import { type Locale, SUPPORTED_LOCALES } from './locale-store';

/**
 * Validates if a string is a supported locale
 */
export function isValidLocale(locale: string): locale is Locale {
  return SUPPORTED_LOCALES.includes(locale as Locale);
}

/**
 * Gets the user's preferred locale from various sources
 * Priority: localStorage > browser language > default
 */
export function getPreferredLocale(): Locale {
  // Check if we're in the browser
  if (typeof window === 'undefined') {
    return 'en'; // Default for server-side
  }

  // Try to get from localStorage first
  try {
    // Try the new key first (Zustand format)
    let stored = localStorage.getItem('language_preference');
    if (stored) {
      const parsed = JSON.parse(stored);
      if (parsed?.state?.locale && isValidLocale(parsed.state.locale)) {
        return parsed.state.locale;
      }
    }

    // Fallback to old key for migration
    stored = localStorage.getItem('locale-preference');
    if (stored) {
      const parsed = JSON.parse(stored);
      if (parsed?.state?.locale && isValidLocale(parsed.state.locale)) {
        // Migrate to new key
        localStorage.setItem('language_preference', stored);
        localStorage.removeItem('locale-preference');
        return parsed.state.locale;
      }
    }
  } catch (error) {
    console.warn('Failed to parse stored locale preference:', error);
  }

  // Fallback to browser language
  const browserLang = navigator.language.split('-')[0];
  if (isValidLocale(browserLang)) {
    return browserLang;
  }

  // Final fallback
  return 'en';
}

/**
 * Loads messages for a specific locale
 */
export async function loadMessages(locale: Locale) {
  try {
    const messages = await import(`../../../messages/${locale}.json`);
    return messages.default;
  } catch (error) {
    console.error(`Failed to load messages for locale ${locale}:`, error);
    // Fallback to English if the locale file doesn't exist
    if (locale !== 'en') {
      const fallback = await import(`../../../messages/en.json`);
      return fallback.default;
    }
    throw error;
  }
}
