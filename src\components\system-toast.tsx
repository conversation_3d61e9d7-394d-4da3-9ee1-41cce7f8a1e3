'use client'

import React, { useEffect, useState } from 'react'
import { emitter } from '@/lib/eventBus'
import { motion, AnimatePresence } from 'framer-motion'
import { RefreshCw, Check, AlertCircle, Loader } from 'lucide-react'
import { useTranslations } from 'next-intl'

/* eslint-disable @typescript-eslint/no-explicit-any */

type ToastStatus = 'reconnecting' | 'reconnected' | 'error' | 'expired' | null

export function SystemToast() {
  const t = useTranslations('SystemToast');
  const [status, setStatus] = useState<ToastStatus>(null)
  const [message, setMessage] = useState('')
  const [visible, setVisible] = useState(false)
  const [willReload, setWillReload] = useState(false)

  useEffect(() => {
    // Set up listeners for system events
    const onReconnecting = ({ message }: { message: string }) => {
      setStatus('reconnecting')
      setMessage(message)
      setVisible(true)
      setWillReload(false)
    }

    const onReconnected = ({ message }: { message: string }) => {
      setStatus('reconnected')
      setMessage(message)
      setVisible(true)
      setWillReload(false)
      
      // Auto-hide success message after 3 seconds
      setTimeout(() => {
        setVisible(false)
      }, 3000)
    }

    const onConnectionError = ({ message }: { message: string }) => {
      setStatus('error')
      setMessage(message)
      setVisible(true)
      setWillReload(false)
    }

    const onSessionExpired = ({ message, willReload }: { message: string; willReload: boolean }) => {
      setStatus('expired')
      setMessage(message)
      setVisible(true)
      setWillReload(willReload)
    }

    // Register event listeners
    emitter.on('system:reconnecting' as any, onReconnecting)
    emitter.on('system:reconnected' as any, onReconnected)
    emitter.on('system:connection_error' as any, onConnectionError)
    emitter.on('auth:session:expired' as any, onSessionExpired)

    // Cleanup
    return () => {
      emitter.off('system:reconnecting' as any, onReconnecting)
      emitter.off('system:reconnected' as any, onReconnected)
      emitter.off('system:connection_error' as any, onConnectionError)
      emitter.off('auth:session:expired' as any, onSessionExpired)
    }
  }, [])

  if (!visible) return null

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className={`fixed top-4 right-4 z-50 px-4 py-3 rounded shadow-lg flex items-center space-x-2 ${
            status === 'reconnecting' ? 'bg-blue-50 text-blue-800 border border-blue-200' :
            status === 'reconnected' ? 'bg-green-50 text-green-800 border border-green-200' :
            status === 'expired' ? 'bg-amber-50 text-amber-800 border border-amber-200' :
            'bg-red-50 text-red-800 border border-red-200'
          }`}
        >
          {status === 'reconnecting' && (
            <div className="animate-spin">
              <RefreshCw className="h-4 w-4" />
            </div>
          )}
          {status === 'reconnected' && <Check className="h-4 w-4" />}
          {status === 'error' && <AlertCircle className="h-4 w-4" />}
          {status === 'expired' && <AlertCircle className="h-4 w-4" />}
          
          <span className="text-sm">{message}</span>
          
          {willReload && (
            <div className="ml-2 flex items-center">
              <Loader className="h-3 w-3 animate-spin mr-1" />
              <span className="text-xs">{t('reloading')}</span>
            </div>
          )}
          
          <button 
            onClick={() => setVisible(false)}
            className="ml-2 opacity-70 hover:opacity-100"
          >
            &times;
          </button>
        </motion.div>
      )}
    </AnimatePresence>
  )
} 