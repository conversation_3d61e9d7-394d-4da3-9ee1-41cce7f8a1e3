"use client";

import { useState, useCallback, useEffect, useRef } from "react";
import {
  DndContext,
  DragEndEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
} from "@dnd-kit/core";
import {
  SortableContext,
  arrayMove,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, Trash2, MoreVertical, ChevronRight } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { LabelCategoryEditDialog } from "@/components/labels/LabelCategoryEditDialog";
import {
  Type,
  Calendar,
  Hash,
  Mail,
  Phone,
  List,
  CheckSquare,
  CircleDot,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { createBrowserClient } from "@supabase/ssr";
import type { PostgrestError } from "@supabase/supabase-js";
import { v4 as uuidv4 } from "uuid";
import React from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { useToastMessages } from "@/hooks/use-toast-messages";
import type { DatabaseLabelCategory } from "@/types/app/dashboard/admin/labels/DatabaseLabelCategory";
import type { LabelCategory } from "@/types/app/dashboard/admin/labels/LabelCategory";
import { LABEL_TYPES } from "@/types/app/dashboard/admin/labels/LabelData";
import type { LabelData, LabelType, Label } from "@/types/app/dashboard/admin/labels/LabelData";
import type { SortableLabelProps } from "@/types/app/dashboard/admin/labels/SortableLabelProps";

const TYPE_ICONS: Record<LabelType, React.FC<{ className?: string }>> = {
  text: Type,
  date: Calendar,
  number: Hash,
  email: Mail,
  phone: Phone,
  select: List,
  checkbox: CheckSquare,
  radio: CircleDot,
} as const;

function LabelTypeIcon({
  type,
  className,
}: {
  type: LabelType;
  className?: string;
}) {
  const Icon = TYPE_ICONS[type];
  if (!Icon) {
    // Default to text icon if type is undefined or invalid
    return <Type {...(className && { className })} />;
  }
  return <Icon {...(className && { className })} />;
}

// Create a new memoized component for the label content
const LabelContent = React.memo(function LabelContent({
  label,
  labelCategory,
  onDelete,
  isExpanded,
  setIsExpanded,
  editValue,
  setEditValue,
  description,
  setDescription,
  type,
  setType,
  handleSave,
  handleCancel,
  handleKeyDown,
  inputRef,
  index,
}: {
  label: LabelData;
  labelCategory: LabelCategory;
  onDelete: (id: number, labelCategoryId: number) => void;
  onSave: (label: LabelData) => void;
  isExpanded: boolean;
  setIsExpanded: (expanded: boolean) => void;
  editValue: string;
  setEditValue: (value: string) => void;
  description: string;
  setDescription: (value: string) => void;
  type: LabelType;
  setType: (value: LabelType) => void;
  handleSave: () => void;
  handleCancel: () => void;
  handleKeyDown: (e: React.KeyboardEvent) => void;
  inputRef: React.RefObject<HTMLInputElement | null>;
  index: number;
}) {
  return (
    <Card
      className={`transition-all ${
        isExpanded ? "p-4 shadow-sm" : "p-3"
      } cursor-move hover:shadow-sm bg-white`}
    >
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 flex-1 pr-2">
            <span className="text-sm text-muted-foreground">
              {labelCategory.displayId}.{index + 1}
            </span>

            <Input
              ref={inputRef}
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              onKeyDown={handleKeyDown}
              className="h-6 text-sm flex-1"
              placeholder="Add label name"
              onClick={(e) => {
                e.stopPropagation();
                setIsExpanded(true);
              }}
            />
          </div>
          <LabelTypeIcon
            type={type}
            className="h-4 w-4 text-muted-foreground"
          />
        </div>

        {isExpanded && (
          <div className="space-y-4 pt-2">
            <div className="space-y-2">
              <div className="text-sm font-medium">Description</div>
              <Textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Add a description..."
                className="min-h-[80px] resize-y"
              />
            </div>

            <div className="space-y-2">
              <div className="text-sm font-medium">Type</div>
              <Select
                value={type}
                onValueChange={(value: LabelType) => setType(value)}
                onOpenChange={(open) => {
                  if (!open) {
                    const selectTrigger = document.activeElement as HTMLElement;
                    selectTrigger?.addEventListener(
                      "keydown",
                      (e) => {
                        if (e.key === "Enter") {
                          handleSave();
                        }
                      },
                      { once: true }
                    );
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {LABEL_TYPES.map((typeValue) => (
                    <SelectItem key={typeValue} value={typeValue}>
                      <div className="flex items-center">
                        <LabelTypeIcon
                          type={typeValue}
                          className="h-4 w-4 mr-2"
                        />
                        {typeValue.charAt(0).toUpperCase() + typeValue.slice(1)}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center justify-between pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(label.id, labelCategory.id)}
                className="text-destructive hover:text-destructive hover:bg-destructive/10"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={handleCancel}>
                  Cancel
                </Button>
                <Button size="sm" onClick={handleSave}>
                  Save
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
});

// Update the SortableLabel component to be a thin wrapper
function SortableLabel({
  label,
  labelCategory,
  onDelete,
  onSave,
  index,
}: SortableLabelProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [editValue, setEditValue] = useState(label.name);
  const [description, setDescription] = useState(label.description);
  const [type, setType] = useState<LabelType>(label.type);
  const inputRef = useRef<HTMLInputElement>(null);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: `${labelCategory.id}.${label.id}`,
    data: {
      label,
      labelCategory,
      index,
    },
  });

  const style = {
    transform: transform
      ? `translate3d(${transform.x}px, ${transform.y}px, 0)`
      : undefined,
    transition: transition || undefined,
    opacity: isDragging ? 0.4 : 1,
    position: "relative" as const,
    zIndex: isDragging ? 1 : 0,
  };

  const handleSave = useCallback(() => {
    onSave({
      ...label,
      name: editValue,
      description,
      type,
    });
    setIsExpanded(false);
  }, [label, editValue, description, type, onSave]);

  const handleCancel = useCallback(() => {
    setEditValue(label.name);
    setDescription(label.description);
    setType(label.type);
    setIsExpanded(false);
  }, [label]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleSave();
      }
    },
    [handleSave]
  );

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="transition-transform duration-200 ease-in-out"
    >
      <LabelContent
        label={label}
        labelCategory={labelCategory}
        onDelete={onDelete}
        onSave={onSave}
        isExpanded={isExpanded}
        setIsExpanded={setIsExpanded}
        editValue={editValue}
        setEditValue={setEditValue}
        description={description}
        setDescription={setDescription}
        type={type}
        setType={setType}
        handleSave={handleSave}
        handleCancel={handleCancel}
        handleKeyDown={handleKeyDown}
        inputRef={inputRef}
        index={index}
      />
    </div>
  );
}

export default function LabelsPage() {
  const [labelCategories, setLabelCategories] = useState<LabelCategory[]>([]);
  const [labels, setLabels] = useState<Label[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [editingLabelCategory, setEditingLabelCategory] =
    useState<LabelCategory | null>(null);
  const [isLabelCategoryDialogOpen, setIsLabelCategoryDialogOpen] =
    useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const toastMessages = useToastMessages();

  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const filteredLabels = useCallback(
    (labelCategoryId: number) => {
      const categoryLabels = labels
        .filter((label) => {
          if (!searchQuery) return label.category_id === labelCategoryId;

          // Search by labelcategory.displayId.label.id format
          const idSearch = searchQuery.match(/^(\d+)\.(\d+)$/);
          if (idSearch) {
            const [, catDisplayId, labelIndex] = idSearch;
            const category = labelCategories.find(
              (cat) => cat.displayId === parseInt(catDisplayId)
            );
            const categoryLabels = labels.filter(
              (l) => l.category_id === category?.id
            );
            const targetLabel = categoryLabels[parseInt(labelIndex) - 1];
            return label === targetLabel;
          }

          // Search by name
          return (
            label.category_id === labelCategoryId &&
            (label.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
              label.description
                .toLowerCase()
                .includes(searchQuery.toLowerCase()))
          );
        })
        .sort((a, b) => a.position - b.position);

      return categoryLabels;
    },
    [labels, searchQuery, labelCategories]
  );

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const [activeCatId] = (active.id as string).split(".");
      const categoryId = parseInt(activeCatId);

      // Get all labels for this category and their current order
      const categoryLabels = labels.filter(
        (label) => label.category_id === categoryId
      );
      const otherLabels = labels.filter(
        (label) => label.category_id !== categoryId
      );

      // Find the indices for reordering
      const oldIndex = categoryLabels.findIndex(
        (item) => `${item.category_id}.${item.id}` === active.id
      );
      const newIndex = categoryLabels.findIndex(
        (item) => `${item.category_id}.${item.id}` === over.id
      );

      // Reorder the category labels
      const reorderedCategoryLabels = arrayMove(
        categoryLabels,
        oldIndex,
        newIndex
      ).map((label, index) => ({
        ...label,
        position: index + 1,
      }));

      // Update UI immediately
      setLabels([...otherLabels, ...reorderedCategoryLabels]);

      // Prepare the update payload
      const updatePayload = reorderedCategoryLabels.map((label) => ({
        uid: label.uid,
        position: label.position,
        name: label.name,
        description: label.description,
        type: label.type,
        category_id: label.category_id,
      }));

      // Update database in the background
      try {
        const { error } = await supabase.from("labels").upsert(updatePayload, {
          onConflict: "uid",
        });

        if (error) throw error;

        toastMessages.form.reorderSuccess();
      } catch (error: unknown) {
        console.error("Error updating label positions:", {
          error,
          message: (error as Error)?.message,
          details: (error as PostgrestError)?.details,
          hint: (error as PostgrestError)?.hint,
        });

        // Revert UI changes on error
        setLabels((prev) => {
          const currentOtherLabels = prev.filter(
            (label) => label.category_id !== categoryId
          );
          return [...currentOtherLabels, ...categoryLabels];
        });

        toastMessages.form.reorderError("Failed to reorder labels");
      }
    }
  };

  const handleDeleteLabel = async (id: number, labelCategoryId: number) => {
    try {
      const { error } = await supabase
        .from("labels")
        .delete()
        .eq("id", id)
        .eq("category_id", labelCategoryId);

      if (error) throw error;

      setLabels((prev) =>
        prev.filter(
          (label) => !(label.id === id && label.category_id === labelCategoryId)
        )
      );
      toastMessages.form.deleteSuccess({ description: "Label deleted" });
    } catch (error) {
      console.error("Error deleting label:", error);
      toastMessages.form.deleteError();
    }
  };

  const handleEditLabelCategory = (labelCategory: LabelCategory) => {
    setEditingLabelCategory(labelCategory);
    setIsLabelCategoryDialogOpen(true);
  };

  const handleSaveLabelCategory = async (
    updatedLabelCategory: Partial<LabelCategory>
  ) => {
    try {
      if (editingLabelCategory) {
        const { data, error } = await supabase
          .from("labelcategories")
          .update({ name: updatedLabelCategory.name })
          .eq("uid", editingLabelCategory.uid)
          .select()
          .single();

        if (error) throw error;

        setLabelCategories((prev) =>
          prev.map((labelCategory) =>
            labelCategory.uid === editingLabelCategory.uid
              ? { ...labelCategory, ...data }
              : labelCategory
          )
        );
        setIsLabelCategoryDialogOpen(false);
        toastMessages.form.saveSuccess({ description: `Category "${updatedLabelCategory.name}" updated` });
      } else {
        // Calculate the next position
        const nextPosition =
          labelCategories.length > 0
            ? Math.max(...labelCategories.map((cat) => cat.position || 0)) + 1
            : 1;

        const { data, error } = await supabase
          .from("labelcategories")
          .insert({
            name: updatedLabelCategory.name,
            uid: uuidv4(),
            position: nextPosition,
          })
          .select()
          .single();

        if (error) throw error;

        setLabelCategories((prev) => [...prev, data]);
        setIsLabelCategoryDialogOpen(false);
        toastMessages.form.createSuccess({ description: `Category "${updatedLabelCategory.name}" created` });
      }
    } catch (error: unknown) {
      const pgError = error as PostgrestError;
      console.error("Error saving category:", pgError);
      toastMessages.form.saveError();
    }
  };

  const handleDeleteLabelCategory = async (labelCategoryId: number) => {
    try {
      const { error } = await supabase
        .from("labelcategories")
        .delete()
        .eq("id", labelCategoryId);

      if (error) throw error;

      setLabelCategories((prev) =>
        prev.filter((labelCategory) => labelCategory.id !== labelCategoryId)
      );
      setLabels((prev) =>
        prev.filter((label) => label.category_id !== labelCategoryId)
      );
      toastMessages.form.deleteSuccess({ description: "Category deleted" });
    } catch (error: unknown) {
      const pgError = error as PostgrestError;
      console.error("Error deleting category:", pgError);
      toastMessages.form.deleteError();
    }
  };

  const handleSaveLabel = async (updatedLabel: LabelData) => {
    try {
      const { data, error } = await supabase
        .from("labels")
        .update({
          name: updatedLabel.name,
          description: updatedLabel.description,
          type: updatedLabel.type,
        })
        .eq("uid", updatedLabel.uid)
        .select()
        .single();

      if (error) throw error;

      setLabels((prev) =>
        prev.map((label) =>
          label.uid === updatedLabel.uid ? { ...label, ...data } : label
        )
      );
      toastMessages.form.saveSuccess({ description: "Label updated" });
    } catch (error: unknown) {
      const pgError = error as PostgrestError;
      console.error("Error saving label:", pgError);
      toastMessages.form.saveError();
    }
  };

  const handleAddLabel = async (displayCategoryId: number) => {
    try {
      // Find the category using displayId
      const category = labelCategories.find(
        (cat) => cat.displayId === displayCategoryId
      );
      if (!category) {
        throw new Error("Category not found");
      }

      const newLabel = {
        uid: uuidv4(),
        name: "",
        description: "",
        type: "text" as LabelType,
        category_id: category.id, // Use the actual database ID
        position:
          labels.filter((l) => l.category_id === category.id).length + 1,
      };

      console.log("Adding new label:", newLabel); // Debug log

      const { data, error } = await supabase
        .from("labels")
        .insert(newLabel)
        .select()
        .single();

      if (error) throw error;

      setLabels((prev) => [...prev, data]);
      toastMessages.form.createSuccess({ description: "Label created" });
    } catch (error: unknown) {
      const pgError = error as PostgrestError;
      console.error("Error adding label:", pgError);
      toastMessages.form.createError();
    }
  };

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [
          { data: labelCategoriesData, error: labelCategoriesError },
          { data: labelsData, error: labelsError },
        ] = await Promise.all([
          supabase.from("labelcategories").select("*").order("position"),
          supabase.from("labels").select("*").order("category_id, id"),
        ]);

        if (labelCategoriesError) throw labelCategoriesError;
        if (labelsError) throw labelsError;

        // Store categories with their original IDs but add a displayId
        const sortedCategories = (labelCategoriesData || []).map(
          (category: DatabaseLabelCategory, index: number) => ({
            ...category,
            displayId: index + 1,
          })
        );

        setLabelCategories(sortedCategories);
        setLabels(labelsData || []);
      } catch (error: unknown) {
        const pgError = error as PostgrestError;
        console.error("Error fetching data:", pgError);
        toastMessages.form.fetchError("labels and categories");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [supabase]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="">
      <div className="mb-6 space-y-4">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator>
              <ChevronRight className="h-4 w-4" />
            </BreadcrumbSeparator>
            <BreadcrumbItem>
              <BreadcrumbLink href="/dashboard/admin">Admin</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator>
              <ChevronRight className="h-4 w-4" />
            </BreadcrumbSeparator>
            <BreadcrumbItem>
              <BreadcrumbPage>Labels</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-[#194852]">Labels</h2>
          <div className="flex items-center space-x-4">
            <Button
              className="bg-[#194852] hover:bg-[#194852]/90 text-white hover:text-white"
              variant="outline"
              onClick={() => {
                setEditingLabelCategory(null);
                setIsLabelCategoryDialogOpen(true);
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Category
            </Button>
          </div>
        </div>
      </div>
      <div className="p-6 mt-6 space-y-6 bg-white rounded-lg shadow">
        <div className="flex justify-between items-center">
          <Input
            placeholder="Search labels... (e.g. 1.1 or label name)"
            className="max-w-sm"
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>

        <div className="grid grid-cols-5 gap-6">
          {labelCategories.map((labelCategory) => (
            <div
              key={labelCategory.id}
              className="space-y-4 bg-gray-100 rounded-b-lg"
            >
              <div className="bg-[#0A2C35] text-white p-3 rounded-t-lg">
                <div className="flex items-center justify-between">
                  <span>
                    {labelCategory.displayId}. {labelCategory.name}
                  </span>
                  <div className="flex items-center space-x-2">
                    <span>{filteredLabels(labelCategory.id).length}</span>
                    <DropdownMenu modal={false}>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 text-white"
                        >
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => handleEditLabelCategory(labelCategory)}
                        >
                          Edit Category
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-destructive"
                          onClick={() =>
                            handleDeleteLabelCategory(labelCategory.id)
                          }
                        >
                          Delete Category
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>

              <div className="space-y-2 p-4 pt-0">
                <DndContext
                  sensors={sensors}
                  onDragEnd={handleDragEnd}
                  collisionDetection={closestCenter}
                >
                  <SortableContext
                    items={filteredLabels(labelCategory.id).map(
                      (label) => `${labelCategory.id}.${label.id}`
                    )}
                    strategy={verticalListSortingStrategy}
                  >
                    <div className="space-y-2">
                      {filteredLabels(labelCategory.id).map((label, index) => (
                        <SortableLabel
                          key={label.uid}
                          label={label}
                          labelCategory={labelCategory}
                          onDelete={handleDeleteLabel}
                          onSave={handleSaveLabel}
                          index={index}
                        />
                      ))}
                    </div>
                  </SortableContext>
                </DndContext>

                <Button
                  variant="ghost"
                  className="w-full justify-start text-muted-foreground"
                  onClick={() => handleAddLabel(labelCategory.displayId)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Label
                </Button>
              </div>
            </div>
          ))}
        </div>

        <LabelCategoryEditDialog
          labelCategory={editingLabelCategory}
          isOpen={isLabelCategoryDialogOpen}
          onClose={() => {
            setIsLabelCategoryDialogOpen(false);
            setEditingLabelCategory(null);
          }}
          onSave={handleSaveLabelCategory}
        />
      </div>
    </div>
  );
}
