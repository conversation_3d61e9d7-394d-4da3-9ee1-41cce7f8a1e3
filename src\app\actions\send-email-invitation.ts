'use server'

import { createClient } from '@/lib/supabase/server'
import { checkRbacPermission } from '@/lib/rbac/permissions-server'
import { createEmailSchema } from '@/lib/auth/validation'
import { resend, EMAIL_SENDERS, EMAIL_CONFIG } from '@/lib/email/resend-client'
import { RoleId } from '@/lib/rbac/roles'
import { randomBytes, createHash } from 'crypto'
import { z } from 'zod'
import { render } from '@react-email/render'
import { InvitationEmail } from '@/lib/email/templates/invitation-email'
import { getServerActionTranslations, getValidationTranslations } from '@/lib/i18n/server-translations'

// Role name mapping for display (database role names are lowercase)
const roleDisplayNames: Record<string, string> = {
  orgclient: 'Client',
  orgaccounting: 'Accounting',
  orgmember: 'Member',
  orgadmin: 'Admin',
  supportadmin: 'Support Admin',
  superadmin: 'Super Admin',
}

// Create validation schema factory that accepts translations
function createInviteSchema(t?: (key: string) => string) {
  return z.object({
    orgId: z.string().uuid(),
    email: createEmailSchema(t),
    roleId: z.number().int().min(1).max(6), // Matches existing RoleId system
    personalMessage: z.string().max(500).optional(),
  });
}

// Default schema for type inference
const defaultInviteSchema = createInviteSchema();
export type SendEmailInvitationData = z.infer<typeof defaultInviteSchema>

export interface SendEmailInvitationResult {
  success: boolean
  error?: string
  invitationId?: string
}

export async function sendEmailInvitation(
  data: SendEmailInvitationData
): Promise<SendEmailInvitationResult> {
  // Get translations for validation and error messages
  const tValidation = await getValidationTranslations();
  const tInvitation = await getServerActionTranslations('invitation');

  try {

    // 1. Validate input with translations
    const inviteSchema = createInviteSchema(tValidation);
    const validatedData = inviteSchema.parse(data)

    // 2. Check RBAC permissions (reuse existing patterns)
    const hasPermission = await checkRbacPermission({
      crMinRole: 'orgMember', // Base permission, refined per page
      orgContext: 'current'
    })

    if (!hasPermission) {
      return { success: false, error: tInvitation('insufficientPermissions') }
    }

    // 2b. Additional role-specific validation (defense-in-depth)
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return { success: false, error: tInvitation('authenticationRequired') }
    }

    const { data: membership } = await supabase
      .from('organization_members')
      .select('org_member_role')
      .eq('user_id', user.id)
      .eq('org_id', validatedData.orgId)
      .single()

    if (membership) {
      const inviterRoleId = membership.org_member_role

      // Enforce role-specific invitation rules
      if (inviterRoleId === RoleId.ORGMEMBER && validatedData.roleId !== RoleId.ORGCLIENT) {
        return { success: false, error: tInvitation('membersCanOnlyInviteClients') }
      }

      if (inviterRoleId === RoleId.ORGADMIN && validatedData.roleId < RoleId.ORGADMIN) {
        return { success: false, error: tInvitation('cannotAssignRolesAboveAdmin') }
      }

      // Prevent assignment of superAdmin/supportAdmin roles via invitations
      if (validatedData.roleId <= RoleId.SUPPORTADMIN) {
        return { success: false, error: tInvitation('cannotAssignSuperAdminRoles') }
      }
    }

    // 3. Check for existing pending invitations
    const { data: existing } = await supabase
      .from('email_invitations')
      .select('id')
      .eq('email', validatedData.email)
      .eq('org_id', validatedData.orgId)
      .in('status', ['created', 'sent', 'delivered', 'failed'])
      .maybeSingle()

    if (existing) {
      return {
        success: false,
        error: tInvitation('pendingInviteExists', { email: validatedData.email })
      }
    }

    // 4. Check if user already exists and is member
    const { data: profile } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', validatedData.email)
      .maybeSingle()

    if (profile) {
      const { data: existingMembership } = await supabase
        .from('organization_members')
        .select('org_id')
        .eq('user_id', profile.id)
        .eq('org_id', validatedData.orgId)
        .maybeSingle()

      if (existingMembership) {
        return {
          success: false,
          error: tInvitation('userAlreadyMember', { email: validatedData.email })
        }
      }
    }

    // 5. Generate secure token and create invitation
    const token = randomBytes(32).toString('base64url')
    const hash = createHash('sha256').update(token).digest('hex')
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days

    const { data: invitation, error: insertError } = await supabase
      .from('email_invitations')
      .insert({
        org_id: validatedData.orgId,
        email: validatedData.email,
        role_id: validatedData.roleId,
        invitation_hash: hash,
        expires_at: expiresAt.toISOString(),
        personal_message: validatedData.personalMessage,
        invited_by: user.id
      })
      .select()
      .single()

    if (insertError) {
      console.error('Failed to create invitation:', insertError)
      return { success: false, error: tInvitation('failedToCreateInvitation') }
    }

    // 6. Get organization and role names for email
    const { data: orgData } = await supabase
      .from('organizations')
      .select('org_name')
      .eq('id', validatedData.orgId)
      .single()

    const { data: roleData } = await supabase
      .from('roles')
      .select('role_name')
      .eq('role_id', validatedData.roleId)
      .single()

    // 7. Send email via Resend using React Email template
    try {
      const inviteUrl = `${EMAIL_CONFIG.BASE_URL}/onboarding?token=${token}`
      const expiresAtFormatted = expiresAt.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      })

      // Get inviter name for personalization
      const { data: inviterProfile } = await supabase
        .from('profiles')
        .select('full_name')
        .eq('id', user.id)
        .single()

      // Render the email template
      const displayRoleName = roleData?.role_name ? roleDisplayNames[roleData.role_name] || roleData.role_name : 'team member'
      const emailProps = {
        organizationName: orgData?.org_name || 'our organization',
        inviterName: inviterProfile?.full_name,
        roleName: displayRoleName,
        acceptUrl: inviteUrl,
        expiresAt: expiresAtFormatted,
        ...(validatedData.personalMessage && { personalMessage: validatedData.personalMessage }),
      }

      const emailHtml = await render(InvitationEmail(emailProps))

      const { data: emailResult } = await resend.emails.send({
        from: EMAIL_SENDERS.INVITATION,
        to: validatedData.email,
        subject: `You've been invited to join ${orgData?.org_name || 'our organization'}`,
        html: emailHtml,
        text: `
You've been invited to join ${orgData?.org_name || 'our organization'} as a ${displayRoleName}.

${validatedData.personalMessage ? `"${validatedData.personalMessage}"\n\n` : ''}

Accept your invitation: ${inviteUrl}

This invitation will expire on ${expiresAtFormatted}. If you didn't expect this invitation, you can safely ignore this email.
        `.trim(),
        headers: {
          'X-Email-Type': 'invitation',
          'X-Organization-Id': validatedData.orgId,
        },
      })

      // Update status to delivered
      await supabase
        .from('email_invitations')
        .update({
          status: 'delivered',
          resend_email_id: emailResult?.id
        })
        .eq('id', invitation.id)

      return { success: true, invitationId: invitation.id }

    } catch (emailError) {
      console.error('Failed to send email:', emailError)

      // Update status to failed
      await supabase
        .from('email_invitations')
        .update({ status: 'failed' })
        .eq('id', invitation.id)

      return { success: false, error: tInvitation('failedToSendEmail') }
    }

  } catch (error) {
    console.error('Send invitation error:', error)

    if (error instanceof z.ZodError) {
      return { success: false, error: tInvitation('invalidInputData') }
    }

    return { success: false, error: tInvitation('internalServerError') }
  }
}
