"use client";

import React, { useState, useEffect } from "react";
import MenuItem from "@/components/MenuItem";
import { navigationData } from "@/data/navigation";
import { BookDashed } from "lucide-react";
import { useDashboard } from "./providers/dashboard-provider";
import { OrganizationSwitcher } from "./organization/organization-switcher";
import { useRbacPermission } from "@/hooks/use-rbac-permission";
import type { NavigationItem } from "@/types/navigation";
import type { Organization } from "@/types/organization";
import { useOrganizationsList } from "@/hooks/use-organizations-list";
import { useTranslations } from 'next-intl';

interface SidebarProps {
  isSidebarCollapsed: boolean;
}

export const Sidebar = ({ isSidebarCollapsed }: SidebarProps) => {
  const {
    orgId,
    roleId,
    isOrgActive,
    isUserActiveInOrg,
    activeOrgName,
  } = useDashboard();

  const { organizations = [] } = useOrganizationsList();

  // Use the consolidated permission hook
  const permissions = useRbacPermission();

  // Translation hooks
  const t = useTranslations('Sidebar');
  const tNav = useTranslations('Navigation');

  // All useState calls must come before any conditional returns
  const [localOrgs, setLocalOrgs] = useState<Organization[]>([]);

  // Use store values as fallback if dashboard context is not available
  const effectiveOrgId = orgId || permissions.currentOrgId;
  const effectiveRoleId = roleId || permissions.currentOrgRoleId;
  const effectiveIsOrgActive = isOrgActive ?? (permissions.currentOrgId ? true : null); // Assume active if we have an org
  const effectiveIsUserActiveInOrg = isUserActiveInOrg ?? (permissions.currentOrgId ? true : null); // Assume active if we have an org

  // Simple effect to update local state when organizations change
  useEffect(() => {
    if (organizations.length > 0) {
      setLocalOrgs(organizations);
    }
  }, [organizations]);

  // ALL useMemo calls must also come before conditional returns
  const activeOrganization: Organization | null = React.useMemo(() => {
    if (!effectiveOrgId) return null;

    // First try to find in the current organizations list
    const fromOrgsList = organizations.find((org: Organization) => org.id === effectiveOrgId) ||
                        localOrgs.find((org: Organization) => org.id === effectiveOrgId);

    if (fromOrgsList) return fromOrgsList;

    // If not found, create a minimal organization object from the store
    if (effectiveOrgId && activeOrgName) {
      return {
        id: effectiveOrgId,
        name: activeOrgName,
        org_member_role: effectiveRoleId,
        isActive: effectiveIsOrgActive,
        org_member_is_active: effectiveIsUserActiveInOrg,
        role: effectiveRoleId === 1 ? 'Super Admin' :
              effectiveRoleId === 2 ? 'Support Admin' :
              effectiveRoleId === 3 ? 'Organization Admin' :
              effectiveRoleId === 4 ? 'Organization Member' :
              effectiveRoleId === 5 ? 'Organization Accounting' :
              effectiveRoleId === 6 ? 'Organization Client' : 'Member'
      } as Organization;
    }

    return null;
  }, [effectiveOrgId, effectiveRoleId, effectiveIsOrgActive, effectiveIsUserActiveInOrg, organizations, localOrgs, activeOrgName]);

  const isActiveOrgDisabled = effectiveIsOrgActive === false;
  const isUserInactive = effectiveIsUserActiveInOrg === false;
  const isSuperAdmin = permissions.isSuperAdmin();
  const shouldShowNavigation = (!isActiveOrgDisabled || (isActiveOrgDisabled && isSuperAdmin)) && !isUserInactive;

  const filteredNavigation = React.useMemo(() => {
    if (!effectiveRoleId) return [];

    if (isActiveOrgDisabled && !isSuperAdmin) return [];

    if (isUserInactive) return [];

    return navigationData
      .filter((section) => {
        if (!section.RbacConditions) return true;
        return permissions.checkPermission(section.RbacConditions);
      })
      .map((section) => ({
        ...section,
        items: section.items.filter((item: NavigationItem) => {
          if (!item.RbacConditions) return true;
          return permissions.checkPermission(item.RbacConditions);
        }),
      }))
      .filter((section) => section.items.length > 0);
  }, [effectiveRoleId, isActiveOrgDisabled, isSuperAdmin, isUserInactive, permissions]);

  // Show loading state during hydration to prevent flashes
  if (permissions.isLoading) {
    return (
      <aside
        className={`bg-[#0A2C35] border-r border-[#e2e8ff1a] h-screen transition-all duration-300 fixed left-0 top-0 z-40 flex flex-col ${
          isSidebarCollapsed ? "w-16" : "w-64"
        }`}
      >
        <div className="flex-none p-4">
          <div className="flex items-center h-12">
            <BookDashed className="w-6 h-6 text-white" />
            {!isSidebarCollapsed && (
              <span className="text-2xl font-bold text-white ml-3">
                {t('appName')}<span className="font-extralight">{t('appNameLight')}</span>
              </span>
            )}
          </div>
        </div>

        {!isSidebarCollapsed && (
          <div className="flex-none px-2 mb-4">
            <div className="flex w-full items-center gap-2 rounded-lg p-2 bg-[#194852]/50 animate-pulse">
              <div className="h-8 w-8 rounded-lg bg-[#295D69]/50"></div>
              <div className="flex-1">
                <div className="h-4 bg-[#295D69]/50 rounded mb-1"></div>
                <div className="h-3 bg-[#295D69]/30 rounded w-2/3"></div>
              </div>
            </div>
          </div>
        )}

        <div className="flex-1 overflow-y-auto px-2">
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="space-y-2">
                {!isSidebarCollapsed && (
                  <div className="h-3 bg-gray-600/30 rounded w-20 mx-4"></div>
                )}
                <div className="space-y-1">
                  {[1, 2].map((j) => (
                    <div key={j} className="h-8 bg-gray-600/20 rounded mx-2 animate-pulse"></div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </aside>
    );
  }

  return (
    <aside
      className={`bg-[#0A2C35] border-r border-[#e2e8ff1a] h-screen transition-all duration-300 fixed left-0 top-0 z-40 flex flex-col  ${
        isSidebarCollapsed ? "w-16" : "w-64"
      }`}
    >
      <div className="flex-none p-4">
        <div className="flex items-center h-12">
          <BookDashed className="w-6 h-6 text-white" />
          {!isSidebarCollapsed && (
            <span className="text-2xl font-bold text-white ml-3">
              {t('appName')}<span className="font-extralight">{t('appNameLight')}</span>
            </span>
          )}
        </div>
      </div>

      {organizations && activeOrganization && (
        <div className="flex-none px-2 mb-4">
          <OrganizationSwitcher
            organizations={localOrgs.length > 0 ? localOrgs : organizations}
            activeOrganization={activeOrganization}
            className="text-white hover:bg-white/10"
          />
        </div>
      )}

      {shouldShowNavigation && (
      <div className="flex-1 overflow-y-auto px-2">
        {filteredNavigation.map((section, sectionIndex) => (
          <div key={`${section.titleKey}-${sectionIndex}`} className="mb-6">
            {!isSidebarCollapsed && (
              <h2 className="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                {tNav(section.titleKey)}
              </h2>
            )}
            <div key={`items-${sectionIndex}`} className="mt-2">
              {section.items.map((item, itemIndex) => (
                <MenuItem
                  key={`${item.href || item.labelKey}-${itemIndex}`}
                  item={item}
                  isCollapsed={isSidebarCollapsed}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
      )}

      {isActiveOrgDisabled && !isSuperAdmin && !isSidebarCollapsed && (
        <div className="px-4 py-3 mt-4 mx-2 bg-red-900/20 rounded-md">
          <p className="text-xs text-white opacity-80">
            Organization disabled. Please use the organization switcher above to select an active organization.
          </p>
        </div>
      )}

      {isUserInactive && !isSidebarCollapsed && (
        <div className="px-4 py-3 mt-4 mx-2 bg-red-900/20 rounded-md">
          <p className="text-xs text-white opacity-80">
            Your account has been disabled in this organization. Please use the organization switcher above to select another organization.
          </p>
        </div>
      )}
    </aside>
  );
};
