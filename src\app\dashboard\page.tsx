import { Breadcrumbs } from "@/components/Breadcrumbs";
import { Charts } from "@/components/Charts";
import { getTranslations } from 'next-intl/server';
import React from "react";

async function DashboardPage() {
  const t = await getTranslations('Pages.dashboard');
  const breadcrumbItems = [{ label: t('breadcrumb'), href: "/dashboard" }];

  return (
    <div className="mx-auto px-6 md:px-0 pb-8 ">
      <div className="mb-8">
        <Breadcrumbs items={breadcrumbItems} />
      </div>

      <div className="p-6 pt-0">
        <h1 className="text-2xl font-bold mb-4">{t('title')}</h1>
        <Charts />
      </div>
    </div>
  );
}

export default DashboardPage;
