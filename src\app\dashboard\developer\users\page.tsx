import { MemberTable, MemberTableBusinessRules } from "@/components/shared/member-table";
import { withRbacPermission } from "@/lib/rbac/permissions-server";
import { createClient } from "@/lib/supabase/server"; // For RBAC HOC context
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Terminal } from "lucide-react";
import { PageProps } from "@/types/app/PageProps";
import { getTranslations } from 'next-intl/server';

// This page is for SuperAdmins/SupportAdmins to view all users across all organizations.
const DeveloperAllUsersPage = async (_props: PageProps) => {
  const t = await getTranslations('DeveloperUsersPage');
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <Terminal className="h-4 w-4" />
          <AlertTitle>{t('authenticationError')}</AlertTitle>
          <AlertDescription>{t('mustBeLoggedIn')}</AlertDescription>
        </Alert>
      </div>
    );
  }

  // Define business rules for SuperAdmins/SupportAdmins managing users across organizations
  // SECURITY: User-level restrictions in getAssignableRolesForOrg() handle role assignment limits
  // SuperAdmins can assign any role, SupportAdmins are limited by the component logic
  const developerUsersBusinessRules: MemberTableBusinessRules = {
    profileEditMinRole: "supportAdmin", // supportAdmin+ can view/edit profiles
    roleEditMinRole: "supportAdmin", // supportAdmin+ can change roles
    // No maxAssignableRole restriction - let user-level logic handle this
    statusChangeMinRole: "supportAdmin", // supportAdmin+ can change status
    deleteMinRole: "supportAdmin", // supportAdmin+ can delete members
    inviteMinRole: "supportAdmin", // supportAdmin+ can invite new members
  };

  // No roleFilters needed, or pass null/undefined, to show all users from all orgs.
  // The MemberTable and its hook should handle fetching all users when organizationIdScope is 'ALL_WITH_SELECTOR'
  // and roleFilters is not restrictive.

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">All System Users & Members</h1>
      <MemberTable
        organizationIdScope="ALL_WITH_SELECTOR" // Enables the organization dropdown in MemberTable
        // roleFilters={undefined} // Omit or pass undefined/null to signify no role filtering initially
        permissionModel="role-based"
        businessRules={developerUsersBusinessRules}
        showOrganizationColumn={true} // Important for this view
        tableTitle="All Users" // MemberTable can use this or its default
        // onInviteUser={() => handleGlobalInvite()} // See notes on previous page for server/client interaction
                                                // For now, button appears, action requires further wiring for server page.
      />
    </div>
  );
};

export default withRbacPermission(
  { rRoles: ["superAdmin", "supportAdmin"] },
  { redirectTo: "/dashboard" }
)(DeveloperAllUsersPage);