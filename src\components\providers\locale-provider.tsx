"use client";

import React, { useEffect, useState, useRef } from 'react';
import { NextIntlClientProvider } from 'next-intl';
import { useLocaleStore, type Locale } from '@/lib/i18n/locale-store';
import { getPreferredLocale, loadMessages } from '@/lib/i18n/locale-utils';

interface LocaleProviderProps {
  children: React.ReactNode;
  initialMessages: any; // Messages from server-side
  initialLocale: Locale;
}

/**
 * LocaleProvider manages client-side locale state and provides translations to components.
 * It follows the same hydration patterns as your existing providers to avoid conflicts.
 */
export function LocaleProvider({
  children,
  initialMessages,
  initialLocale
}: LocaleProviderProps) {
  const [messages, setMessages] = useState(initialMessages);
  const [isHydrated, setIsHydrated] = useState(false);
  const initRef = useRef(false);

  // Get locale state from Zustand store
  const { locale, isLoading, setLocale, setLoading } = useLocaleStore();

  // Handle initial hydration - similar to your auth context pattern
  useEffect(() => {
    const handleInitialHydration = async () => {
      try {
        // Get user's preferred locale (localStorage -> browser -> default)
        const preferredLocale = getPreferredLocale();

        // If preferred locale differs from server initial, update store and load messages
        if (preferredLocale !== initialLocale) {
          setLocale(preferredLocale);
          const newMessages = await loadMessages(preferredLocale);
          setMessages(newMessages);
        }

        setIsHydrated(true);
        setLoading(false);
      } catch (error) {
        console.error('[LocaleProvider] Hydration error:', error);
        // Fallback to initial state on error
        setMessages(initialMessages);
        setIsHydrated(true);
        setLoading(false);
      }
    };

    // Only run once on mount
    if (!initRef.current) {
      initRef.current = true;
      handleInitialHydration();
    }
  }, []); // Remove all dependencies to prevent loops

  // Handle locale changes after hydration
  useEffect(() => {
    if (!isHydrated || isLoading) return;

    const loadNewMessages = async () => {
      try {
        setLoading(true);
        const newMessages = await loadMessages(locale);
        setMessages(newMessages);
        setLoading(false);
      } catch (error) {
        console.error('[LocaleProvider] Failed to load messages for locale:', locale, error);
        // Keep current messages on error
        setLoading(false);
      }
    };

    // Only load new messages if locale actually changed and we have messages
    if (locale !== initialLocale && messages) {
      loadNewMessages();
    }
  }, [locale, isHydrated]); // Remove problematic dependencies

  // Prevent hydration mismatch by using server locale until client hydrates
  const currentLocale = isHydrated ? locale : initialLocale;
  const currentMessages = isHydrated ? messages : initialMessages;

  return (
    <NextIntlClientProvider
      locale={currentLocale}
      messages={currentMessages}
      timeZone="UTC" // You can make this dynamic later if needed
    >
      {children}
    </NextIntlClientProvider>
  );
}
