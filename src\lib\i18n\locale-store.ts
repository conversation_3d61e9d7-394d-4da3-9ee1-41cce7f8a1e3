import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type Locale = 'en' | 'nl' | 'th';

export const SUPPORTED_LOCALES: Locale[] = ['en', 'nl', 'th'];

export const LOCALE_NAMES: Record<Locale, string> = {
  en: 'English',
  nl: 'Nederlands',
  th: 'ไทย'
};

interface LocaleStore {
  locale: Locale;
  isLoading: boolean;
  setLocale: (locale: Locale) => void;
  setLoading: (loading: boolean) => void;
}

export const useLocaleStore = create<LocaleStore>()(
  persist(
    (set) => ({
      locale: 'en', // Default locale
      isLoading: false, // Start with false to prevent hydration issues
      setLocale: (locale: Locale) => {
        if (SUPPORTED_LOCALES.includes(locale)) {
          set({ locale, isLoading: false });
        }
      },
      setLoading: (loading: boolean) => set({ isLoading: loading }),
    }),
    {
      name: 'language_preference', // Use ISO 639-1 codes as requested
      partialize: (state) => ({ locale: state.locale }), // Only persist locale, not loading state
      skipHydration: true, // Prevent hydration issues
    }
  )
);
