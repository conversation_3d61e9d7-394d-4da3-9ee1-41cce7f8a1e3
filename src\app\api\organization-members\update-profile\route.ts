import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'
import { canEditUserProfile } from '@/lib/permission-utils'
import type { OrganizationMemberBasic } from '@/types/organization/OrganizationMember'

export async function PATCH(request: Request) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the request body
    const body = await request.json()
    const { userId, profileData } = body

    if (!userId || !profileData) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Find the target member's organization membership
    const { data: targetMember, error: targetMemberError } = await supabase
      .from('organization_members')
      .select('org_id, org_member_role, org_member_is_active')
      .eq('user_id', userId)
      .eq('org_member_is_active', true)
      .limit(1)
      .single()

    if (targetMemberError) {
      console.error('Error finding target member:', targetMemberError)
      return NextResponse.json({ error: 'Member not found' }, { status: 404 })
    }

    // Find the acting user's role in the target organization
    const { data: userRoleData, error: userRoleError } = await supabase
      .from('organization_members')
      .select('org_member_role')
      .eq('org_id', targetMember.org_id)
      .eq('user_id', user.id)
      .eq('org_member_is_active', true)
      .single()

    // Check if users are in the same organization
    const isSameOrg = !userRoleError && !!userRoleData
    
    // If not found in same org, check if superadmin in any org
    let actingUserRoleId: number | undefined = userRoleData?.org_member_role

    if (!isSameOrg) {
      const { data: superAdminCheck } = await supabase
        .from('organization_members')
        .select('org_member_role')
        .eq('user_id', user.id)
        .eq('org_member_role', 1) // Superadmin role
        .limit(1)
      
      if (superAdminCheck && superAdminCheck.length > 0) {
        actingUserRoleId = superAdminCheck[0].org_member_role
      }
    }

    // Build the member object for permission check
    const memberData: OrganizationMemberBasic = {
      user_id: userId,
      org_id: targetMember.org_id,
      org_member_role: targetMember.org_member_role,
      org_member_is_active: targetMember.org_member_is_active
    }

    // Check if user has permission using our domain-specific utility
    const hasPermission = canEditUserProfile(
      memberData,
      actingUserRoleId || Number.MAX_SAFE_INTEGER,
      user.id,
      isSameOrg
    )

    if (!hasPermission) {
      return NextResponse.json({
        error: 'Insufficient permissions to update this user\'s profile'
      }, { status: 403 })
    }

    // Update the personal info in the user_personal_information table
    const { data: updatedData, error: updateError } = await supabase
      .from('user_personal_information')
      .upsert({
        id: userId,
        ...profileData,
        updated_by: user.id,
        updated_at: new Date().toISOString()
      })
      .select()

    if (updateError) {
      console.error('Error updating profile:', updateError)
      return NextResponse.json({ error: 'Failed to update profile' }, { status: 500 })
    }

    return NextResponse.json({
      message: 'Profile updated successfully',
      data: updatedData
    })
  } catch (error) {
    console.error('Unexpected error in update-profile endpoint:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
} 