'use client'

import { useState } from 'react'
import { sendEmailInvitation } from '@/app/actions/send-email-invitation'
import { <PERSON>Key } from '@/lib/rbac/rbac-utils'
import { RoleId } from '@/lib/rbac/roles'
import { z } from 'zod'
import { toast } from 'sonner'
import { useOrganizationsList } from '@/hooks/use-organizations-list'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Loader2, Mail } from 'lucide-react'
import { TurnstileCaptcha } from '@/components/auth/turnstile-captcha'
import { useTranslations } from 'next-intl'

const emailSchema = z.string().email('Please enter a valid email address')

interface SendInvitationModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  allowedRoles: RoleKey[]
  orgId?: string | undefined // For developer invitations
  showOrgSelector?: boolean
}

// Role mapping for display
const roleDisplayNames: Record<RoleKey, string> = {
  orgClient: 'Client',
  orgAccounting: 'Accounting',
  orgMember: 'Member',
  orgAdmin: 'Admin',
  supportAdmin: 'Support Admin',
  superAdmin: 'Super Admin',
}

const roleIds: Record<RoleKey, number> = {
  orgClient: RoleId.ORGCLIENT,
  orgAccounting: RoleId.ORGACCOUNTING,
  orgMember: RoleId.ORGMEMBER,
  orgAdmin: RoleId.ORGADMIN,
  supportAdmin: RoleId.SUPPORTADMIN,
  superAdmin: RoleId.SUPERADMIN,
}

export default function SendInvitationModal({
  isOpen,
  onClose,
  onSuccess,
  allowedRoles,
  orgId,
  showOrgSelector = false,
}: SendInvitationModalProps) {
  const t = useTranslations('SendInvitationModal');
  // Determine default role - prefer orgClient if available, otherwise first allowed role
  const getDefaultRole = (): RoleKey | '' => {
    if (allowedRoles.includes('orgClient')) return 'orgClient'
    return allowedRoles.length > 0 ? allowedRoles[0] : ''
  }

  const [email, setEmail] = useState('')
  const [selectedRole, setSelectedRole] = useState<RoleKey | ''>(getDefaultRole())
  const [personalMessage, setPersonalMessage] = useState('')
  const [selectedOrgId, setSelectedOrgId] = useState(orgId || '')
  const [captchaToken, setCaptchaToken] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [emailError, setEmailError] = useState<string | null>(null)

  // Get organizations list for developer invitations
  const { organizations } = useOrganizationsList()

  const handleEmailChange = (value: string) => {
    setEmail(value)
    if (value) {
      const result = emailSchema.safeParse(value)
      setEmailError(result.success ? null : t('emailPlaceholder'))
    } else {
      setEmailError(null)
    }
  }

  const handleCaptchaSuccess = (token: string) => {
    setCaptchaToken(token)
  }

  const handleCaptchaError = () => {
    setCaptchaToken(null)
    toast.error('CAPTCHA verification failed. Please try again.')
  }

  const handleCaptchaExpire = () => {
    setCaptchaToken(null)
    toast.error('CAPTCHA expired. Please verify again.')
  }

  const canSubmit = () => {
    return (
      email &&
      !emailError &&
      selectedRole &&
      captchaToken &&
      !isSubmitting &&
      (!showOrgSelector || selectedOrgId)
    )
  }

  const handleSubmit = async () => {
    if (!canSubmit()) return

    // Determine the target organization ID
    const targetOrgId = selectedOrgId || orgId
    if (!targetOrgId) {
      toast.error('Please select an organization')
      return
    }

    try {
      setIsSubmitting(true)

      const result = await sendEmailInvitation({
        orgId: targetOrgId,
        email: email.trim(),
        roleId: roleIds[selectedRole as RoleKey],
        personalMessage: personalMessage.trim() || undefined,
      })

      if (result.success) {
        toast.success('Invitation sent successfully!')
        onSuccess()
        handleClose()
      } else {
        toast.error(result.error || 'Failed to send invitation')
      }
    } catch (error) {
      console.error('Send invitation error:', error)
      toast.error('An unexpected error occurred. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    setEmail('')
    setSelectedRole(getDefaultRole()) // Reset to default role
    setPersonalMessage('')
    setSelectedOrgId(orgId || '')
    setCaptchaToken(null)
    setEmailError(null)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            {t('title')}
          </DialogTitle>
          <DialogDescription>
            Send an email invitation to join the organization.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Email Field */}
          <div className="space-y-2">
            <Label htmlFor="email">{t('email')}</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => handleEmailChange(e.target.value)}
              placeholder={t('emailPlaceholder')}
              className={emailError ? 'border-red-500' : ''}
            />
            {emailError && (
              <p className="text-sm text-red-600 dark:text-red-400">{emailError}</p>
            )}
          </div>

          {/* Organization Selector (for developer invitations) */}
          {showOrgSelector && (
            <div className="space-y-2">
              <Label htmlFor="organization">{t('organization')}</Label>
              <Select value={selectedOrgId} onValueChange={setSelectedOrgId}>
                <SelectTrigger>
                  <SelectValue placeholder={t('selectOrganization')} />
                </SelectTrigger>
                <SelectContent>
                  {organizations.length > 0 ? (
                    organizations
                      .sort((a, b) => a.name.localeCompare(b.name)) // Sort alphabetically
                      .map((org) => (
                        <SelectItem key={org.id} value={org.id}>
                          {org.name}
                        </SelectItem>
                      ))
                  ) : (
                    <SelectItem value="" disabled>
                      No organizations available
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Role Selector */}
          <div className="space-y-2">
            <Label htmlFor="role">{t('role')}</Label>
            <Select value={selectedRole} onValueChange={(value) => setSelectedRole(value as RoleKey)}>
              <SelectTrigger>
                <SelectValue placeholder={t('selectRole')} />
              </SelectTrigger>
              <SelectContent>
                {allowedRoles.map((role) => (
                  <SelectItem key={role} value={role}>
                    {t(`roles.${role}`)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Personal Message */}
          <div className="space-y-2">
            <Label htmlFor="message">Personal Message (Optional)</Label>
            <Textarea
              id="message"
              value={personalMessage}
              onChange={(e) => setPersonalMessage(e.target.value)}
              placeholder="Add a personal message to the invitation..."
              rows={3}
              maxLength={500}
            />
            <p className="text-xs text-gray-500">
              {personalMessage.length}/500 characters
            </p>
          </div>

          {/* CAPTCHA */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Security Verification</Label>
            <TurnstileCaptcha
              onSuccess={handleCaptchaSuccess}
              onError={handleCaptchaError}
              onExpire={handleCaptchaExpire}
            />
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
            {t('cancel')}
          </Button>
          <Button onClick={handleSubmit} disabled={!canSubmit()}>
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                {t('sending')}
              </>
            ) : (
              <>
                <Mail className="h-4 w-4 mr-2" />
                {t('sendInvitation')}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
