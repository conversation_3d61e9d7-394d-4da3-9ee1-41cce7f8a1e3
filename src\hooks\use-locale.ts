"use client";

import { useLocaleStore, type Locale, SUPPORTED_LOCALES, LOCALE_NAMES } from '@/lib/i18n/locale-store';

/**
 * Hook for managing locale state and actions.
 * Follows the same pattern as your existing hooks (useDashboard, etc.)
 */
export function useLocale() {
  const { locale, isLoading, setLocale } = useLocaleStore();

  const changeLocale = (newLocale: Locale) => {
    if (SUPPORTED_LOCALES.includes(newLocale)) {
      setLocale(newLocale);
    } else {
      console.warn(`[useLocale] Unsupported locale: ${newLocale}`);
    }
  };

  return {
    locale,
    isLoading,
    changeLocale,
    supportedLocales: SUPPORTED_LOCALES,
    localeNames: LOCALE_NAMES,
  };
}
